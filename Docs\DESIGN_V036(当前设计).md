# 神灯AI·灵阅 V0.36 - 当前设计文档

## 🎨 设计理念

### 核心设计原则
- **用户至上**：以用户体验为中心，简化操作流程
- **无干扰性**：不影响用户正常的浏览体验
- **智能化**：自动化处理复杂逻辑，减少用户操作
- **可访问性**：支持不同用户群体的使用需求

### 设计目标
- **降低阅读门槛**：让任何人都能轻松使用语音阅读
- **提升阅读效率**：通过连续播放减少手动操作
- **保持专注度**：通过语音播放让用户专注于内容本身
- **个性化体验**：提供丰富的自定义选项

## 🏗️ 架构设计

### 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Chrome Extension                         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ Side Panel  │  │   Service   │  │   Content Script    │  │
│  │   (UI)      │◄─┤   Worker    │◄─┤   (Page Parser)     │  │
│  │             │  │  (Logic)    │  │                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ Chrome TTS  │  │   Chrome    │  │    Readability.js   │  │
│  │    API      │  │  Storage    │  │      Library        │  │
│  │             │  │    API      │  │                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### 模块职责

#### 1. Side Panel (UI层)
- **职责**：用户界面展示和交互处理
- **核心文件**：`src/ui/sidepanel/index.js`
- **主要功能**：
  - 播放控制界面
  - 状态显示和更新
  - 设置参数配置
  - 历史记录管理

#### 2. Service Worker (逻辑层)
- **职责**：核心业务逻辑处理
- **核心文件**：`src/background/index.js`
- **主要功能**：
  - TTS播放控制
  - 连续阅读逻辑
  - 状态管理
  - 消息路由

#### 3. Content Script (解析层)
- **职责**：页面内容解析和链接查找
- **核心文件**：`src/content/parser.js`
- **主要功能**：
  - 页面内容提取
  - 下一页链接查找
  - 与后台脚本通信

### 数据流设计
```
用户操作 → UI事件 → 消息传递 → Service Worker处理 → 
状态更新 → UI反馈 → 用户感知
```

## 🎯 UI/UX设计

### 界面设计原则

#### 1. 简洁性
- **最小化界面元素**：只显示必要的控件
- **清晰的视觉层次**：重要功能突出显示
- **一致的设计语言**：统一的颜色、字体、间距

#### 2. 易用性
- **直观的操作逻辑**：符合用户习惯的交互方式
- **即时反馈**：操作后立即显示结果
- **容错设计**：防止用户误操作

#### 3. 响应性
- **实时状态更新**：播放状态实时同步
- **快速响应**：操作响应时间<300ms
- **流畅动画**：平滑的状态转换

### 核心界面设计

#### 1. 主控制面板
```
┌─────────────────────────────────┐
│ 🔵 神灯AI·灵阅 V0.36           │
├─────────────────────────────────┤
│ [URL输入框]                     │
│ ▼ 播放历史                      │
├─────────────────────────────────┤
│     ⏸️  ⏹️                     │
│   [播放控制]                    │
│                                 │
│ ████████░░░░ 67%               │
│ [进度条] 00:10:30              │
├─────────────────────────────────┤
│ 🔊 语音设置                     │
│ 📖 连续阅读: ✅ 30分钟 30章     │
└─────────────────────────────────┘
```

#### 2. 语音设置页面
```
┌─────────────────────────────────┐
│ 语音设置                    ✕   │
├─────────────────────────────────┤
│ [搜索框]                        │
├─────────────────────────────────┤
│ ○ Microsoft Xiaoxiao (中文)     │
│ ● Microsoft Xiaoyi (中文) 🔊    │
│ ○ Google 中文 (中国大陆)        │
│ ○ English (US) - Female         │
├─────────────────────────────────┤
│          [保存设置]             │
└─────────────────────────────────┘
```

### 交互设计

#### 1. 播放控制
- **单击播放**：播放当前页面或URL内容
- **长按播放**：显示播放选项菜单
- **拖拽进度条**：跳转到指定时间点
- **滚轮调速**：在语速滑块上使用滚轮快速调节

#### 2. 历史管理
- **点击播放**：直接播放历史记录
- **右键菜单**：删除、重命名等操作
- **拖拽排序**：调整历史记录顺序

## 🔧 技术选型

### 核心技术栈

#### 1. Chrome Extension Manifest V3
**选择理由**：
- **现代化**：Google推荐的最新扩展架构
- **安全性**：更严格的权限控制和安全机制
- **性能**：Service Worker提供更好的性能
- **未来兼容**：V2将被逐步淘汰

#### 2. Chrome TTS API
**选择理由**：
- **原生支持**：无需额外依赖
- **高质量**：系统级语音合成
- **多语言**：支持多种语言和语音
- **免费使用**：无API调用限制

#### 3. Readability.js
**选择理由**：
- **成熟稳定**：Mozilla开源项目，经过大量测试
- **智能提取**：能够准确识别正文内容
- **广泛兼容**：支持各种网站结构
- **轻量级**：文件大小适中，不影响性能

#### 4. Chrome Storage API
**选择理由**：
- **持久化**：数据在浏览器重启后保持
- **同步支持**：可选的跨设备同步
- **简单易用**：API设计简洁
- **权限控制**：安全的数据存储

### 架构模式

#### 1. 事件驱动架构
- **消息传递**：组件间通过消息通信
- **松耦合**：各模块独立开发和测试
- **可扩展**：易于添加新功能模块

#### 2. 状态管理模式
- **集中状态**：Service Worker作为状态中心
- **单向数据流**：状态变更通过消息传播
- **持久化**：关键状态自动保存

#### 3. 模块化设计
- **功能分离**：每个模块负责特定功能
- **接口标准**：统一的模块间接口
- **可维护性**：代码结构清晰，易于维护

### 性能优化策略

#### 1. 代码优化
- **按需加载**：只加载必要的代码
- **代码分割**：将功能模块分离
- **压缩优化**：生产版本代码压缩

#### 2. 内存管理
- **智能缓存**：合理的内容缓存策略
- **及时清理**：不用的数据及时释放
- **内存监控**：监控内存使用情况

#### 3. 网络优化
- **本地优先**：优先使用本地资源
- **批量操作**：减少网络请求次数
- **错误重试**：网络失败时自动重试

## 🎨 视觉设计

### 色彩方案
- **主色调**：深蓝色 (#1a365d) - 专业、可信
- **辅助色**：浅蓝色 (#3182ce) - 活力、现代
- **成功色**：绿色 (#38a169) - 积极、完成
- **警告色**：橙色 (#dd6b20) - 注意、提醒
- **错误色**：红色 (#e53e3e) - 错误、停止

### 字体系统
- **主字体**：系统默认字体栈
- **代码字体**：等宽字体 (Consolas, Monaco)
- **图标字体**：Unicode符号和Emoji

### 间距系统
- **基础单位**：4px
- **小间距**：8px (2单位)
- **中间距**：16px (4单位)
- **大间距**：24px (6单位)
- **超大间距**：32px (8单位)

## 📊 实现状态

### 已实现功能
- ✅ 基础TTS播放系统
- ✅ 连续阅读引擎
- ✅ 智能内容解析
- ✅ 播放历史管理
- ✅ URL直接播放
- ✅ 语音设置界面
- ✅ 状态持久化

### 设计特色
- **紧凑界面**：优化的空间利用
- **实时反馈**：即时的状态更新
- **智能适配**：自动适应不同网站
- **无缝体验**：流畅的操作流程
