# 神灯AI·灵阅 V0.36 - 开发指南

## 🚀 开发环境搭建

### 系统要求
- **操作系统**：Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)
- **浏览器**：Chrome 88+ 或 Edge 88+
- **Node.js**：16.0+ (可选，用于开发工具)
- **编辑器**：VS Code (推荐) 或其他支持JavaScript的编辑器

### 快速开始

#### 1. 获取源码
```bash
# 克隆仓库
git clone https://github.com/your-repo/神灯AI·灵阅.git
cd 神灯AI·灵阅

# 或直接下载ZIP文件并解压
```

#### 2. 安装扩展
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 启用右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目根目录

#### 3. 验证安装
- 扩展图标应出现在浏览器工具栏
- 点击图标打开侧边栏
- 尝试播放当前页面内容

## 📁 项目结构

```
神灯AI·灵阅/
├── 📁 src/                     # 源代码目录
│   ├── 📁 background/          # 后台脚本
│   │   └── index.js           # 主后台脚本
│   ├── 📁 content/            # 内容脚本
│   │   └── parser.js          # 页面解析器
│   ├── 📁 ui/                 # 用户界面
│   │   ├── 📁 sidepanel/      # 侧边栏界面
│   │   ├── 📁 voiceSettings/  # 语音设置页面
│   │   └── 📁 options/        # 选项页面
│   ├── 📁 lib/                # 第三方库
│   │   └── Readability.js     # 内容提取库
│   ├── 📁 assets/             # 静态资源
│   │   └── 📁 icons/          # 图标文件
│   └── 📁 utils/              # 工具函数
├── 📁 Docs/                   # 项目文档
├── manifest.json              # 扩展配置文件
├── package.json               # 项目配置
└── README.md                  # 项目说明
```

## 🔧 核心模块开发

### 1. 后台脚本开发 (`src/background/index.js`)

#### 主要功能模块
```javascript
// TTS播放控制
function speakText(text, tabId) {
  // TTS播放逻辑
}

// 连续播放逻辑
async function simpleContinuousPlay(tabId) {
  // 连续播放实现
}

// 状态管理
function savePlaybackState() {
  // 状态持久化
}

// 消息处理
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // 消息路由处理
});
```

#### 开发注意事项
- **Service Worker生命周期**：注意SW的启动和休眠机制
- **状态持久化**：重要状态必须保存到Storage
- **错误处理**：完善的try-catch和错误恢复
- **性能优化**：避免长时间运行的同步操作

### 2. 内容脚本开发 (`src/content/parser.js`)

#### 核心功能
```javascript
// 页面内容解析
function parsePageContent() {
  // 使用Readability.js解析内容
}

// 查找下一页链接
function findNextPageLink() {
  // 关键词匹配查找
}

// 消息通信
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // 处理来自后台的消息
});
```

#### 开发要点
- **DOM操作安全**：确保DOM已加载完成
- **网站兼容性**：测试不同网站的适配性
- **性能考虑**：避免阻塞页面渲染
- **错误容错**：处理各种异常情况

### 3. UI界面开发 (`src/ui/sidepanel/`)

#### 组件结构
```javascript
// 状态管理
let currentState = {
  readingState: 'idle',
  article: null,
  settings: {}
};

// 事件处理
function handlePlayPause() {
  // 播放/暂停逻辑
}

// UI更新
function updateUI(state) {
  // 根据状态更新界面
}
```

#### UI开发规范
- **响应式设计**：适配不同屏幕尺寸
- **无障碍访问**：支持键盘导航和屏幕阅读器
- **性能优化**：避免频繁的DOM操作
- **用户体验**：提供即时反馈和加载状态

## 🐛 调试方法

### 1. Chrome开发者工具

#### 后台脚本调试
1. 访问 `chrome://extensions/`
2. 找到扩展，点击"检查视图"中的"Service Worker"
3. 在Console中查看日志和错误

#### 内容脚本调试
1. 在目标网页按F12打开开发者工具
2. 在Console中查看内容脚本的日志
3. 在Sources面板中设置断点

#### UI界面调试
1. 右键点击侧边栏，选择"检查"
2. 使用Elements面板查看DOM结构
3. 使用Console面板测试JavaScript代码

### 2. 日志系统

#### 统一日志格式
```javascript
// 使用统一的日志前缀
console.log("🎵 TTS: 开始播放");
console.warn("⚠️ Parser: 内容解析失败");
console.error("❌ Background: API调用错误");
```

#### 调试开关
```javascript
const DEBUG = true;

function debugLog(message, data) {
  if (DEBUG) {
    console.log(`[DEBUG] ${message}`, data);
  }
}
```

### 3. 常见问题排查

#### 扩展无法加载
- 检查manifest.json语法
- 确认文件路径正确
- 查看扩展管理页面的错误信息

#### TTS不工作
- 检查TTS权限是否授予
- 确认系统语音是否可用
- 查看后台脚本的错误日志

#### 内容解析失败
- 检查Readability.js是否正确加载
- 确认网站内容结构
- 查看内容脚本的执行情况

## 🧪 测试策略

### 1. 功能测试

#### 基础功能测试
- [ ] TTS播放/暂停/停止
- [ ] 语速调节
- [ ] 语音切换
- [ ] 内容解析
- [ ] 连续播放
- [ ] 历史记录

#### 兼容性测试
- [ ] 不同网站适配
- [ ] 不同浏览器版本
- [ ] 不同操作系统
- [ ] 不同语音引擎

### 2. 性能测试

#### 内存使用
```javascript
// 监控内存使用
setInterval(() => {
  if (performance.memory) {
    console.log('Memory:', {
      used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
      total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB'
    });
  }
}, 10000);
```

#### 响应时间
```javascript
// 测量操作响应时间
const startTime = performance.now();
// 执行操作
const endTime = performance.now();
console.log(`操作耗时: ${endTime - startTime}ms`);
```

## 📝 代码规范

### 1. JavaScript规范

#### 命名约定
```javascript
// 变量和函数：驼峰命名
const currentState = {};
function handlePlayPause() {}

// 常量：大写下划线
const MAX_CHAPTERS = 999;
const DEFAULT_SPEED = 1.0;

// 类：帕斯卡命名
class TTSManager {}
```

#### 函数规范
```javascript
/**
 * 播放文本内容
 * @param {string} text - 要播放的文本
 * @param {number} tabId - 标签页ID
 * @param {number} startPosition - 开始位置
 * @returns {Promise<void>}
 */
async function speakText(text, tabId, startPosition = 0) {
  // 参数验证
  if (!text || typeof text !== 'string') {
    throw new Error('Invalid text parameter');
  }
  
  // 实现逻辑
  try {
    // ...
  } catch (error) {
    console.error('播放失败:', error);
    throw error;
  }
}
```

### 2. HTML/CSS规范

#### HTML结构
```html
<!-- 语义化标签 -->
<main class="container">
  <section class="controls-section">
    <button id="play-btn" class="control-btn primary">
      播放
    </button>
  </section>
</main>
```

#### CSS命名
```css
/* BEM命名规范 */
.control-btn {
  /* 基础样式 */
}

.control-btn--primary {
  /* 主要按钮样式 */
}

.control-btn__icon {
  /* 按钮图标样式 */
}
```

## 🤝 贡献流程

### 1. 开发流程

#### 功能开发
1. **创建分支**：从main分支创建功能分支
2. **开发实现**：按照代码规范进行开发
3. **测试验证**：完成功能测试和兼容性测试
4. **代码审查**：提交Pull Request进行代码审查
5. **合并发布**：审查通过后合并到main分支

### 2. 提交规范

#### Commit消息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 类型说明
- **feat**: 新功能
- **fix**: 问题修复
- **docs**: 文档更新
- **style**: 代码格式调整
- **refactor**: 代码重构
- **test**: 测试相关
- **chore**: 构建工具、辅助工具等

#### 示例
```
feat(tts): 添加语音预览功能

- 在语音设置页面添加预览按钮
- 支持实时试听不同语音效果
- 优化语音列表的加载性能

Closes #123
```

## 📚 学习资源

### Chrome扩展开发
- [Chrome Extensions Documentation](https://developer.chrome.com/docs/extensions/)
- [Manifest V3 Migration Guide](https://developer.chrome.com/docs/extensions/migrating/)
- [Chrome APIs Reference](https://developer.chrome.com/docs/extensions/reference/)

### 相关技术
- [Web Speech API](https://developer.mozilla.org/en-US/docs/Web/API/Web_Speech_API)
- [Readability.js](https://github.com/mozilla/readability)
- [Chrome Storage API](https://developer.chrome.com/docs/extensions/reference/storage/)
