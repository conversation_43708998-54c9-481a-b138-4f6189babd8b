# 神灯AI·灵阅 V0.36

一个基于AI技术的智能阅读增强浏览器插件，为用户提供高质量的文本朗读和智能阅读体验。

## 🎯 项目概述

神灯AI·灵阅是一个现代化的Chrome浏览器扩展，专为提升网页阅读体验而设计。通过智能内容解析、高质量语音合成和连续阅读技术，让用户能够更轻松、更高效地消费网络内容。

### 核心价值
- **解放双眼**：通过语音播放减少视觉疲劳
- **提升效率**：连续播放功能实现无缝阅读体验
- **智能适配**：自动适配各类网站的内容结构
- **个性化体验**：丰富的语音和播放设置选项

## 🚀 主要功能

### 🎵 智能朗读系统
- **高质量TTS**：基于Chrome TTS API的语音合成
- **多语音支持**：支持系统内置的各种语音
- **语速控制**：0.1x-3.0x无级调速
- **实时预览**：语音设置页面支持实时试听

### 📖 连续阅读引擎
- **智能翻页**：自动识别"下一页"、"下一章"链接
- **无缝播放**：章节间自动切换，无需手动干预
- **时长控制**：支持设置播放时长（最长12小时）
- **章节限制**：可设置连续播放章节数（1-999章）

### 🎯 智能内容解析
- **多站点适配**：支持小说网站、新闻网站、博客等
- **内容提取**：使用Readability.js智能提取正文
- **去噪处理**：自动过滤广告、导航等无关内容
- **URL播放**：支持直接输入URL进行播放

### 📚 播放历史管理
- **自动记录**：播放历史自动保存
- **智能去重**：同一小说只保留最新章节
- **快速重播**：点击历史记录快速播放
- **批量管理**：支持删除和清空操作

### 📱 现代化界面
- **侧边栏设计**：不干扰正常浏览体验
- **紧凑布局**：优化的UI设计，节省空间
- **状态指示**：实时显示播放状态和进度
- **响应式设计**：适配不同屏幕尺寸

## 🔧 技术特色

### 现代化架构
- **Manifest V3**：基于最新的Chrome扩展架构
- **Service Worker**：高效的后台处理机制
- **模块化设计**：清晰的代码结构和职责分离

### 核心技术栈
- **Chrome TTS API**：原生语音合成支持
- **Readability.js**：Mozilla开源内容提取库
- **Chrome Storage API**：设置和状态持久化
- **Chrome Scripting API**：动态脚本注入

### 性能优化
- **轻量级设计**：核心代码约3000行
- **零依赖**：除Readability.js外无第三方依赖
- **高效缓存**：智能的内容缓存机制
- **错误恢复**：完善的错误处理和状态恢复

## 📦 安装使用

### 开发版安装
1. 克隆或下载项目源码
2. 在Chrome浏览器中打开 `chrome://extensions/`
3. 启用右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目根目录

### 使用方法
1. **基础播放**：点击侧边栏的播放按钮播放当前页面
2. **URL播放**：在URL输入框中输入网址直接播放
3. **连续播放**：启用连续阅读开关，设置时长和章节数
4. **语音设置**：点击语音设置按钮选择和预览语音
5. **历史管理**：查看和管理播放历史记录

## 📊 版本信息

- **当前版本**：V0.36
- **发布日期**：2024年12月
- **开发状态**：活跃开发中
- **兼容性**：Chrome 88+

### 版本特性
- ✅ 完整的TTS播放功能
- ✅ 连续阅读和自动翻页
- ✅ 智能内容解析
- ✅ 播放历史管理
- ✅ URL直接播放
- ✅ 语音设置和预览
- ✅ 状态持久化

## 🤝 贡献指南

欢迎提交Issue和Pull Request来帮助改进项目。

### 开发环境
- Node.js 16+
- Chrome 88+
- 基础的JavaScript/HTML/CSS知识

### 项目结构
```
神灯AI·灵阅/
├── src/                    # 源代码
│   ├── background/         # 后台脚本
│   ├── content/           # 内容脚本
│   ├── ui/                # 用户界面
│   └── lib/               # 第三方库
├── Docs/                  # 项目文档
├── manifest.json          # 扩展配置
└── README.md             # 项目说明
```

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件
