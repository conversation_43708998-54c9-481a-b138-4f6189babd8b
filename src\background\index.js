// Service Worker for 神灯 AI 朗读增强助手

console.log("🚀 Service Worker started - 神灯AI·灵阅 V0.36");

// 立即测试基础功能
try {
  console.log("✅ 基础JavaScript功能正常");

  // 测试Chrome API可用性
  if (typeof chrome !== 'undefined') {
    console.log("✅ Chrome API可用");

    if (chrome.action) {
      console.log("✅ chrome.action API可用");
    } else {
      console.error("❌ chrome.action API不可用");
    }

    if (chrome.sidePanel) {
      console.log("✅ chrome.sidePanel API可用");
    } else {
      console.error("❌ chrome.sidePanel API不可用");
    }

    if (chrome.runtime) {
      console.log("✅ chrome.runtime API可用");
    } else {
      console.error("❌ chrome.runtime API不可用");
    }
  } else {
    console.error("❌ Chrome API完全不可用");
  }
} catch (error) {
  console.error("❌ Service Worker初始化时出错:", error);
}

// --- State Management ---
let readingState = 'idle'; // idle, reading, paused, navigating
let currentSpeed = 1.0;
let currentVoice = null;
let continuousReading = true;
let continuousChapters = 30;
let readingDuration = 30 * 60; // 默认30分钟 (1800秒)
let tabContentCache = {}; // Stores { tabId: articleObject }
let currentReadingTabId = null; // Track which tab is currently being read
let currentUtterance = null; // Track the current TTS utterance index/position if splitting text
let currentChapterCount = 0; // 章节计数
let nextChapterInfo = null;
let playbackStartTime = null; // 播放开始时间
let playbackTimer = null; // 播放时长计时器

// 添加状态持久化函数
function savePlaybackState() {
  try {
    const stateToSave = {
      readingState,
      currentSpeed,
      currentVoice,
      continuousReading,
      continuousChapters,
      readingDuration,
      currentReadingTabId,
      currentChapterCount,
      utterancePosition: currentUtterance ? currentUtterance.position : 0,
      timestamp: Date.now()
    };
    
    console.log("保存播放状态:", stateToSave);
    
    // 使用chrome.storage.local保存状态
    if (isStorageAvailable) {
      chrome.storage.local.set({
        'playback_state': stateToSave
      }, () => {
        if (chrome.runtime.lastError) {
          console.error("保存播放状态失败:", chrome.runtime.lastError);
        } else {
          console.log("播放状态已保存");
        }
      });
    }
  } catch (e) {
    console.error("保存播放状态时出错:", e);
  }
}

// 添加恢复状态函数
function restorePlaybackState() {
  try {
    if (isStorageAvailable) {
      chrome.storage.local.get(['playback_state'], (result) => {
        if (chrome.runtime.lastError) {
          console.error("恢复播放状态失败:", chrome.runtime.lastError);
          return;
        }
        
        if (result && result.playback_state) {
          const savedState = result.playback_state;
          const timestamp = savedState.timestamp;
          
          // 只恢复最近30分钟内的状态
          if (timestamp && (Date.now() - timestamp < 30 * 60 * 1000)) {
            console.log("从存储恢复播放状态:", savedState);
            
            // 恢复基本设置
            currentSpeed = savedState.currentSpeed || currentSpeed;
            currentVoice = savedState.currentVoice || currentVoice;
            continuousReading = savedState.continuousReading !== undefined ? savedState.continuousReading : continuousReading;
            continuousChapters = savedState.continuousChapters || continuousChapters;
            readingDuration = savedState.readingDuration || readingDuration;
            currentChapterCount = savedState.currentChapterCount || 0;
            
            // 恢复阅读状态
            if (savedState.readingState === 'reading' || savedState.readingState === 'paused') {
              // 检查是否还有要恢复的标签页
              if (savedState.currentReadingTabId) {
                chrome.tabs.get(savedState.currentReadingTabId, (tab) => {
                  if (tab) {
                    currentReadingTabId = tab.id;
                    readingState = savedState.readingState;
                    
                    // 如果有位置信息，恢复位置
                    if (typeof savedState.utterancePosition === 'number') {
                      currentUtterance = { position: savedState.utterancePosition };
                    }
                    
                    // 通知UI状态已恢复
                    console.log("播放状态已成功恢复，通知UI更新");
                    sendStateUpdate();
                  } else {
                    console.log("恢复状态时，找不到原阅读标签页，重置状态");
                    readingState = 'idle';
                    currentReadingTabId = null;
                    currentUtterance = null;
                  }
                });
              } else {
                console.log("恢复状态时，无有效的标签页ID，重置状态");
                readingState = 'idle';
              }
            } else {
              // 恢复为idle状态
              readingState = 'idle';
              sendStateUpdate();
            }
          } else {
            console.log("保存的状态过期，使用默认状态");
          }
        } else {
          console.log("未找到保存的播放状态，使用默认状态");
        }
      });
    }
  } catch (e) {
    console.error("恢复播放状态时出错:", e);
  }
}

// 在使用API前检查其可用性
const isChromeAvailable = typeof chrome !== 'undefined';
const isRuntimeAvailable = isChromeAvailable && typeof chrome.runtime !== 'undefined';
const isActionAvailable = isChromeAvailable && typeof chrome.action !== 'undefined';
const isSidePanelAvailable = isChromeAvailable && typeof chrome.sidePanel !== 'undefined';
const isTtsAvailable = isChromeAvailable && typeof chrome.tts !== 'undefined';
const isContextMenusAvailable = isChromeAvailable && typeof chrome.contextMenus !== 'undefined';
const isTabsAvailable = isChromeAvailable && typeof chrome.tabs !== 'undefined';
const isStorageAvailable = isChromeAvailable && typeof chrome.storage !== 'undefined';
const isScriptingAvailable = isChromeAvailable && typeof chrome.scripting !== 'undefined';
const isWindowsAvailable = isChromeAvailable && typeof chrome.windows !== 'undefined';

console.log("API可用性检查:", {
  isChromeAvailable,
  isRuntimeAvailable,
  isActionAvailable, 
  isSidePanelAvailable,
  isTtsAvailable,
  isContextMenusAvailable,
  isTabsAvailable,
  isStorageAvailable,
  isScriptingAvailable,
  isWindowsAvailable
});

// Function to send state updates to the side panel
function sendStateUpdate(tabId = null) {
  // 使用当前选项卡ID或currentReadingTabId
  const targetTabId = tabId || currentReadingTabId;
  
  if (!targetTabId) {
    console.warn("BG: 无法发送状态更新：无有效的标签页ID");
    return;
  }
  
  // 获取活动窗口
  const getActiveWindow = () => {
    return new Promise((resolve) => {
      if (isWindowsAvailable) {
        chrome.windows.getCurrent((window) => {
          resolve(window);
        });
      } else {
        resolve(null);
      }
    });
  };
  
  getActiveWindow().then(window => {
    // 确保窗口有效
    if (!window) {
      console.warn("BG: 无法获取当前窗口");
      return;
    }
    
    // 构建状态对象
    const state = {
      readingState: readingState,
      currentSpeed: currentSpeed,
      currentVoice: currentVoice,
      continuousReading: continuousReading,
      continuousChapters: continuousChapters,
      readingDuration: readingDuration,
      remainingPlaybackTime: getRemainingPlaybackTime(), // 添加剩余播放时间
      currentChapterTitle: targetTabId && tabContentCache[targetTabId] ? tabContentCache[targetTabId].title : null,
      article: targetTabId ? tabContentCache[targetTabId] : null, // Include article for the target tab
      error: null, // TODO: Add error state if needed
      position: currentUtterance ? currentUtterance.position : null
    };
    
    // 添加预加载信息
    if (nextChapterInfo) {
      console.log("BG: 添加预加载信息到状态更新:", nextChapterInfo.title);
      state.nextChapterInfo = {
        title: nextChapterInfo.title,
        isPreloaded: true,
        isFullyPreloaded: nextChapterInfo.isFullyPreloaded || false,
        url: nextChapterInfo.url
      };
    }
    
    console.log("DEBUG BG: Sending state update with readingState =", readingState);
    console.log("DEBUG BG: 完整状态:", JSON.stringify(state));
    
    // 保存状态，确保可以在刷新后恢复
    if (readingState === 'reading' || readingState === 'paused' || readingState === 'navigating') {
      savePlaybackState();
    }
    
    // 发送状态更新到侧边栏
    if (isSidePanelAvailable) {
      console.log("BG: 发送状态更新到侧边栏");
      try {
        chrome.runtime.sendMessage({
          action: 'stateUpdate',
          state: state
        });
      } catch (e) {
        console.error("BG: 发送消息到侧边栏失败:", e);
      }
    }
  });
}

// --- Initial Setup ---
if (isRuntimeAvailable) {
  chrome.runtime.onInstalled?.addListener(details => {
    console.log("Extension installed or updated:", details);
    loadSettings();
  });

  chrome.runtime.onStartup?.addListener(() => {
    console.log("Browser started, loading settings...");
    loadSettings();
  });
}

// 加载设置的函数
function loadSettings() {
  if (!isStorageAvailable) {
    console.warn("无法加载设置：chrome.storage API不可用");
    return;
  }

  chrome.storage.local.get(['currentSpeed', 'currentVoice', 'continuousReading', 'readingDuration', 'continuousChapters'], (result) => {
    readingState = 'idle'; // Always start idle
    currentSpeed = result.currentSpeed || 1.1; // 默认语速改为1.1
    currentVoice = result.currentVoice || null;
    continuousReading = result.continuousReading !== undefined ? result.continuousReading : true;
    readingDuration = result.readingDuration || 30 * 60; // 默认30分钟 (1800秒)
    continuousChapters = result.continuousChapters || 30; // 默认连续阅读30章
    tabContentCache = {}; // Clear cache on install/update
    currentReadingTabId = null;
    currentUtterance = null;
    currentChapterCount = 0; // 重置章节计数
    console.log("BG: Settings loaded:", { readingState, currentSpeed, currentVoice, continuousReading, readingDuration, continuousChapters });
    
    // 清理过期的文章元数据
    cleanupArticleMetadata();
    
    // 尝试恢复播放状态
    restorePlaybackState();
  });
}

/**
 * 清理存储中过期的文章元数据
 * 只保留最近7天内的条目，避免数据堆积
 */
function cleanupArticleMetadata() {
  console.log("BG: 开始清理过期的文章元数据...");
  
  chrome.storage.local.get(null, (items) => {
    // 查找所有article_metadata_前缀的键
    const metadataKeys = Object.keys(items).filter(key => 
      key.startsWith('article_metadata_')
    );
    
    console.log(`BG: 找到 ${metadataKeys.length} 条文章元数据记录`);
    
    // 如果超过50条记录，只保留最近的30条
    if (metadataKeys.length > 50) {
      // 按照tabId排序，保留更高的TabId（通常是较新的）
      const keysToRemove = metadataKeys
        .sort((a, b) => {
          const idA = parseInt(a.replace('article_metadata_', '')) || 0;
          const idB = parseInt(b.replace('article_metadata_', '')) || 0;
          return idA - idB;
        })
        .slice(0, metadataKeys.length - 30);
      
      console.log(`BG: 将清理 ${keysToRemove.length} 条旧记录`);
      
      // 批量删除过期记录
      chrome.storage.local.remove(keysToRemove, () => {
        console.log("BG: 过期文章元数据清理完成");
      });
    } else {
      console.log("BG: 文章元数据数量在合理范围内，无需清理");
    }
  });
}

// Context Menu for starting reading
if (isContextMenusAvailable) {
  try {
    // 先移除已存在的菜单项，防止重复创建
    chrome.contextMenus.removeAll(() => {
      console.log("已清除现有上下文菜单项");
      
      // 创建新的菜单项
      chrome.contextMenus.create({
        id: "read-selection",
        title: "使用神灯 AI 阅读选中内容",
        contexts: ["selection"]
      });
      
      chrome.contextMenus.create({
        id: "read-page",
        title: "使用神灯 AI 阅读页面",
        contexts: ["page"]
      });
    });

    // Listen for context menu clicks
    chrome.contextMenus.onClicked.addListener((info, tab) => {
      if (!isSidePanelAvailable || !isTtsAvailable) {
        console.warn("无法执行阅读操作：sidePanel或tts API不可用");
        return;
      }

      if (info.menuItemId === "read-selection" && info.selectionText) {
        console.log("BG: Context menu - read selection clicked");
        // Stop any current reading
        chrome.tts.stop();
        readingState = 'reading';
        currentReadingTabId = tab.id;
        // Store selection as a temporary article
        tabContentCache[tab.id] = { title: "选中文本", textContent: info.selectionText };
        speakText(info.selectionText, tab.id);
        sendStateUpdate(tab.id);
        chrome.sidePanel.open({ windowId: tab.windowId }); // Open side panel

      } else if (info.menuItemId === "read-page") {
        console.log("BG: Context menu - read page clicked");
        console.log(`BG: Assuming parser already ran for tab ${tab.id}. Opening side panel.`);
        chrome.sidePanel.open({ windowId: tab.windowId });
        sendStateUpdate(tab.id);
      }
    });
  } catch (e) {
    console.error("创建上下文菜单失败:", e);
  }
}

// Listener for messages from content scripts or UI
if (isRuntimeAvailable) {
  try {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      console.log("BG: Message received:", message, "from sender:", sender);
      const tabId = sender.tab?.id;

      switch (message.action) {
        case "parsedContent":
          if (tabId && message.article) {
            console.log(`BG: Received parsed content from tab ${tabId}`);
            
            // 保存解析的文章内容到内存缓存
            tabContentCache[tabId] = message.article;

            // 同时将文章元数据持久化保存到storage中
            const articleMetadata = {
              title: message.article.title || "未命名文章",
              byline: message.article.byline || "",
              siteName: message.article.siteName || "",
              excerpt: message.article.excerpt || ""
            };

            // 使用tabId作为key存储元数据
            chrome.storage.local.set({
              [`article_metadata_${tabId}`]: articleMetadata
            }, () => {
              console.log(`BG: 已持久化保存文章元数据: ${articleMetadata.title}`);
            });

            // 立即添加到播放历史记录
            addToPlaybackHistory(message.article, tabId);
            
            // 检查是否是播放按钮发起的自动刷新
            chrome.storage.session.get(['autoRefreshForPlay', 'autoRefreshOrigin'], (data) => {
              if (data.autoRefreshForPlay === tabId) {
                // 清除标记
                chrome.storage.session.remove('autoRefreshForPlay');
                console.log(`BG: 检测到播放按钮的自动刷新，开始朗读`);

                // 恢复位置信息
                let startPosition = 0;
                let previousState = 'idle';

                if (data.autoRefreshOrigin) {
                  startPosition = data.autoRefreshOrigin.position || 0;
                  previousState = data.autoRefreshOrigin.state || 'idle';
                  console.log(`BG: 恢复自动刷新前的位置: ${startPosition}, 状态: ${previousState}`);

                  // 清除原始信息
                  chrome.storage.session.remove('autoRefreshOrigin');
                }

                // 开始朗读（自动刷新后的播放）
                readingState = 'reading';
                // 如果有保存的位置，从该位置开始朗读
                speakText(message.article.textContent, tabId, startPosition);
                currentReadingTabId = tabId;
                sendStateUpdate(tabId);
                if (sendResponse) {
                  sendResponse({ status: "播放按钮自动刷新后开始朗读" });
                }
              } else {
                // 检查是否是由播放按钮触发的解析（状态为loading）
                if (readingState === 'loading') {
                  console.log("BG: 检测到播放按钮触发的解析，自动开始播放");
                  readingState = 'reading';
                  currentReadingTabId = tabId;
                  continuousSpeakText(message.article.textContent, tabId);
                  sendStateUpdate(tabId);
                  if (sendResponse) {
                    sendResponse({ status: "解析完成，开始播放" });
                  }
                } else if (readingState === 'reading' || readingState === 'paused') {
                  console.log(`BG: 已有朗读在进行(${readingState})，不改变状态`);
                  // 仅缓存内容，不更改状态或发送更新
                  if (sendResponse) {
                    sendResponse({ status: "内容已缓存，现有朗读不受影响" });
                  }
                } else {
                  // 普通解析，设置为idle状态
                  readingState = 'idle';
                  currentReadingTabId = tabId;
                  sendStateUpdate(tabId);
                  sendResponse({ status: "Parsed content received and cached" });
                }
              }
            });
            
            return true; // 异步响应
          } else {
            console.error("BG: Invalid parsedContent message", message);
            sendResponse({ error: "Invalid parsedContent message" });
          }
          break;

        case "startReading":
          console.log("BG: 收到startReading请求，当前状态:", readingState);
          console.log("BG: currentReadingTabId:", currentReadingTabId);
          console.log("BG: tabContentCache keys:", Object.keys(tabContentCache));

          if (currentReadingTabId && tabContentCache[currentReadingTabId]) {
            const article = tabContentCache[currentReadingTabId];
            if (article.textContent) {
              console.log("BG: 开始播放，标签页:", currentReadingTabId);
              chrome.tts.stop(); // Stop previous speech first
              console.log("BG: 播放前状态:", readingState);
              speakText(article.textContent, currentReadingTabId);
              readingState = 'reading';
              console.log("BG: 播放后状态:", readingState);
              sendStateUpdate();
              console.log("BG: startReading 完成");
              sendResponse({ status: "Reading started" });
            } else {
              console.warn("BG: No text content found in cached article for tab:", currentReadingTabId);
              readingState = 'idle';
              sendStateUpdate();
              sendResponse({ error: "No text content found" });
            }
          } else {
            console.warn("BG: startReading requested but no content cached or tab ID mismatch. Current Tab:", currentReadingTabId);

            // 确定要使用的标签页ID
            const targetTabId = currentReadingTabId || tabId;

            if (targetTabId) {
              console.log("BG: 尝试触发内容解析，标签页:", targetTabId);
              readingState = 'loading';
              currentReadingTabId = targetTabId; // 设置当前阅读标签页ID
              sendStateUpdate();

              // 设置解析超时
              const parseTimeout = setTimeout(() => {
                if (readingState === 'loading') {
                  console.warn("BG: 内容解析超时，重置状态");
                  readingState = 'idle';
                  sendStateUpdate();
                }
              }, 10000); // 10秒超时

              // 向内容脚本发送解析请求
              chrome.tabs.sendMessage(targetTabId, { action: 'parseContent' }, (response) => {
                clearTimeout(parseTimeout); // 清除超时

                if (chrome.runtime.lastError) {
                  console.error("BG: 发送解析请求失败:", chrome.runtime.lastError.message);
                  readingState = 'idle';
                  sendStateUpdate();
                  if (sendResponse) {
                    sendResponse({ error: "无法解析页面内容，请刷新页面后重试" });
                  }
                } else {
                  console.log("BG: 解析请求已发送，等待解析结果");
                  // 解析结果会通过 parsedContent 消息返回，在那里处理播放
                  if (sendResponse) {
                    sendResponse({ status: "正在解析页面内容..." });
                  }
                }
              });
            } else {
              readingState = 'idle';
              sendStateUpdate();
              sendResponse({ error: "没有可用的页面内容" });
            }
          }
          break;

        case "pauseReading":
          console.log("BG: pauseReading message received, 当前状态:", readingState);
          
          if (readingState === 'reading' && isTtsAvailable) {
            try {
              // 保存当前位置用于恢复
              getCurrentUtterancePosition((position) => {
                const utterance = currentUtterance;
                console.log("BG: Paused at position:", position);
                
                // 使用stop函数停止TTS
                chrome.tts.stop();

                // 暂停播放时长计时器
                pausePlaybackDurationTimer();

                // 设置为暂停状态
                readingState = 'paused';
                
                // 保存当前位置
                if (utterance) {
                  utterance.resumePosition = position;
                  currentUtterance = utterance;
                }
                
                // 通知UI更新
                sendStateUpdate();
                
                // 响应消息
                sendResponse({ 
                  currentState: 'paused', 
                  currentPosition: position 
                });
              });
            } catch (e) {
              console.error("暂停阅读时出错:", e);
              sendResponse({ 
                error: '暂停朗读失败: ' + e.message,
                currentState: readingState 
              });
            }
          } else {
            sendResponse({ 
              currentState: readingState, 
              message: "没有进行中的朗读，无法暂停"
            });
          }
          break;

        case "resumeReading": // Sidepanel currently doesn't have resume, uses play
          if (readingState === 'paused' && currentReadingTabId && tabContentCache[currentReadingTabId]) {
            console.log("BG: 恢复朗读，当前标签页:", currentReadingTabId);
            console.log("BG: 当前保存的朗读位置:", currentUtterance?.position);
            
            // 从暂停位置继续播放
            // 如果有保存当前朗读位置，则从该位置继续
            if (currentUtterance && typeof currentUtterance.position === 'number') {
                console.log("BG: 从位置继续:", currentUtterance.position);
                
                try {
                    // 从保存的位置继续播放
                    const article = tabContentCache[currentReadingTabId];
                    
                    if (!article || !article.textContent) {
                        console.error("BG: 无法恢复朗读 - 文章内容丢失");
                        readingState = 'idle';
                        currentUtterance = null;
                        sendStateUpdate();
                        sendResponse({ error: "无法恢复朗读 - 文章内容丢失" });
                        return;
                    }
                    
                    // 验证位置有效性
                    if (currentUtterance.position < 0 || currentUtterance.position >= article.textContent.length) {
                        console.warn("BG: 保存的位置无效，从头开始:", currentUtterance.position);
                        speakText(article.textContent, currentReadingTabId);
                    } else {
                        // 有效位置，从保存的位置继续
                        const remainingText = article.textContent.substring(currentUtterance.position);
                        console.log(`BG: 从位置 ${currentUtterance.position} 继续，剩余文本长度: ${remainingText.length}`);
                        speakText(remainingText, currentReadingTabId, currentUtterance.position);
                    }
                } catch (error) {
                    console.error("BG: 恢复朗读时出错:", error);
                    // 发生错误时从头开始
                    const article = tabContentCache[currentReadingTabId];
                    speakText(article.textContent, currentReadingTabId);
                }
            } else {
                // 如果没有保存位置，则从头开始（兼容旧逻辑）
                console.log("BG: 无保存位置，从头开始");
                const article = tabContentCache[currentReadingTabId];
                speakText(article.textContent, currentReadingTabId);
            }
            
            readingState = 'reading';

            // 恢复播放时长计时器
            resumePlaybackDurationTimer();

            sendStateUpdate();
            sendResponse({ status: "已从上次位置继续朗读" });
          } else {
            console.warn("BG: 无法恢复朗读 - 状态不是暂停或没有内容");
            sendResponse({ status: "非暂停状态，无法恢复" });
          }
          break;

        case "stopReading":
          console.log("BG: stopReading message received", message, "当前状态:", readingState, "当前朗读标签页:", currentReadingTabId);
          // 确保已启用TTS
          if (isTtsAvailable) {
            // 使用集中的停止函数
            stopTTSCompletely("stop_button");
          }
          
          // 响应消息
          sendResponse({ currentState: readingState });
          break;

        case "setSpeed":
          if (typeof message.speed === 'number') {
            const previousSpeed = currentSpeed;
            currentSpeed = Math.max(0.5, Math.min(5.0, message.speed)); // 限制语速范围在0.5-5.0之间
            console.log("BG: Setting speed to", currentSpeed, "from", previousSpeed);
            chrome.storage.local.set({ currentSpeed });
            
            // 记录当前状态和位置
            const wasReading = readingState === 'reading';
            const currentPosition = currentUtterance ? currentUtterance.position : 0;
            
            if (wasReading) {
              console.log("BG: 朗读状态下更新语速，使用重启方法确保生效");

              // 直接使用重启方法，这是最可靠的方式
              const previousState = readingState;
              readingState = 'changing_speed';
              sendStateUpdate(); // 通知UI正在调整中

              chrome.tts.stop(() => {
                if (currentReadingTabId && tabContentCache[currentReadingTabId]) {
                  const article = tabContentCache[currentReadingTabId];
                  // 计算从当前位置开始的剩余文本
                  if (article.textContent && currentPosition >= 0) {
                    const safePosition = Math.min(currentPosition, article.textContent.length - 1);
                    const remainingText = article.textContent.substring(safePosition > 0 ? safePosition : 0);

                    // 恢复之前的状态
                    readingState = previousState;

                    // 从当前位置继续朗读，使用新语速
                    continuousSpeakText(remainingText, currentReadingTabId, safePosition);
                    console.log(`BG: 已从位置 ${safePosition} 以新语速 ${currentSpeed} 重新开始朗读`);

                    sendResponse({
                      status: "Speed updated with restart",
                      currentSpeed,
                      wasInterrupted: true
                    });
                  } else {
                    // 无法获取准确位置，从头开始
                    console.warn("BG: 无法确定精确位置，从头开始朗读");
                    readingState = previousState;
                    speakText(article.textContent, currentReadingTabId);

                    sendResponse({
                      status: "Speed updated with restart from beginning",
                      currentSpeed,
                      wasInterrupted: true
                    });
                  }
                } else {
                  // 恢复状态但不重启朗读
                  console.warn("BG: 找不到当前标签页或内容缓存");
                  readingState = previousState;
                  sendStateUpdate();

                  sendResponse({
                    status: "Speed updated but no content to restart",
                    currentSpeed,
                    wasInterrupted: false
                  });
                }
              });
            } else {
              // 非朗读状态，只需更新设置
              sendStateUpdate();
              sendResponse({ 
                status: "Speed updated (not reading)", 
                currentSpeed, 
                wasInterrupted: false 
              });
            }
            
            return true; // 异步响应
          }
          break;

        case "setContinuousReading":
          if (typeof message.continuous === 'boolean') {
            continuousReading = message.continuous;
            console.log("BG: Setting continuous reading to", continuousReading);
            chrome.storage.local.set({ continuousReading });
            sendStateUpdate();
            sendResponse({ status: "Continuous reading updated" });
          }
          break;

        case "setContinuousChapters":
          if (typeof message.chapters === 'number') {
            continuousChapters = message.chapters;
            console.log("BG: Setting continuous chapters to", continuousChapters);
            chrome.storage.local.set({ continuousChapters });
            sendStateUpdate();
            sendResponse({ status: "Continuous chapters updated" });
          }
          break;

        case "setReadingDuration":
          if (typeof message.payload === 'number') {
            readingDuration = message.payload;
            console.log("BG: Setting reading duration to", readingDuration);
            chrome.storage.local.set({ readingDuration });
            sendStateUpdate();
            sendResponse({ status: "Reading duration updated" });
          } else {
            console.error("BG: Invalid reading duration:", message.payload);
            sendResponse({ error: "Invalid reading duration" });
          }
          break;

        case "getState":
          // Determine the relevant tab ID - might be sender's tab or the one currently reading
          const relevantTabId = currentReadingTabId || tabId;
          sendStateUpdate(relevantTabId); // Send current state back
          // sendResponse is implicitly handled by sendStateUpdate (it sends a message)
          // but the listener expects a direct response sometimes? Let's send one.
          sendResponse({ status: "State requested, update sent separately" });
          break;

        case "getVoices":
          console.log("处理getVoices请求");
          if (isTtsAvailable) {
            chrome.tts.getVoices(voices => {
              console.log(`获取到 ${voices?.length || 0} 个语音`);
              if (sendResponse) {
                sendResponse({ voices: voices || [] });
              }
            });
            return true; // 异步响应
          } else {
            console.warn("TTS API 不可用，无法获取语音列表");
            if (sendResponse) {
              sendResponse({ voices: [], error: "TTS API不可用" });
            }
          }
          break;

        case "setVoice":
          if (typeof message.payload === 'string') {
            currentVoice = message.payload;
            console.log("BG: Setting voice to", currentVoice);
            chrome.storage.local.set({ currentVoice });
            sendStateUpdate();
            sendResponse({ status: "Voice updated" });
          }
          break;

        case "updateVoiceOptions":
          console.log("BG: 收到语音设置更新:", message);
          
          // 更新默认语音
          if (message.defaultVoice !== undefined) {
            currentVoice = message.defaultVoice;
            console.log("BG: 更新默认语音为:", currentVoice);
            chrome.storage.local.set({ currentVoice });
          }
          
          // 保存收藏的语音列表到storage
          if (message.favoriteVoices) {
            console.log("BG: 更新收藏语音列表:", message.favoriteVoices);
            chrome.storage.local.set({ favoriteVoices: message.favoriteVoices });
          }
          
          // 保存筛选标签状态到storage
          if (message.voiceFilters) {
            console.log("BG: 更新语音筛选状态:", message.voiceFilters);
            chrome.storage.local.set({ voiceFilters: message.voiceFilters });
          }
          
          // 向所有已连接的页面广播更新，确保侧边栏和其他页面都能收到
          chrome.tabs.query({}, (tabs) => {
            tabs.forEach(tab => {
              try {
                chrome.tabs.sendMessage(tab.id, {
                  action: 'updateVoiceOptions',
                  defaultVoice: message.defaultVoice,
                  favoriteVoices: message.favoriteVoices,
                  voiceFilters: message.voiceFilters
                });
              } catch (error) {
                console.error(`向标签页 ${tab.id} 发送更新时出错:`, error);
              }
            });
          });
          
          // 同时发送状态更新
          sendStateUpdate();
          sendResponse({ status: "Voice settings updated and broadcasted" });
          break;

        case "sidePanelClosing":
          console.log("BG: Received sidePanelClosing message:", message);
          
          // 高优先级处理侧边栏关闭消息
          if (isTtsAvailable) {
            // 检查是否有force标志（表示这是明确的关闭操作）
            const isForceClose = message.force === true;
            console.log(`BG: 侧边栏关闭，强制停止: ${isForceClose}，当前状态: ${readingState}`);
            
            // 无论当前状态如何，都立即停止TTS
            try {
              // 立即停止TTS，不依赖于stopTTSCompletely函数
              chrome.tts.stop(() => {
                console.log("BG: 侧边栏关闭，TTS立即停止完成");
              });
            } catch (e) {
              console.error("BG: 侧边栏关闭时直接停止TTS出错:", e);
            }
            
            // 然后使用更完整的停止函数
            stopTTSCompletely("explicit_panel_close");
            
            // 清理本地存储中的关闭标记
            try {
              // 使用setTimeout确保其他操作先完成
              setTimeout(() => {
                // 检查localStorage中是否有关闭标记
                if (typeof localStorage !== 'undefined') {
                  localStorage.removeItem('sidepanel_closing');
                  localStorage.removeItem('sidepanel_closing_timestamp');
                  localStorage.removeItem('sidepanel_closing_state');
                }
              }, 100);
            } catch (storageError) {
              console.error("BG: 清理localStorage关闭标记失败:", storageError);
            }
            
            // 记录到storage用于调试
            try {
              chrome.storage.local.set({
                last_panel_closing_message: {
                  timestamp: Date.now(),
                  sender: sender?.id || 'unknown',
                  hadForceFlag: isForceClose,
                  previousState: readingState,
                  currentState: 'idle' // 已重置为idle
                }
              });
            } catch (storageError) {
              console.error("BG: 保存panel closing message状态失败:", storageError);
            }
            
            sendResponse({ status: "Reading stopped due to panel close" });
          } else {
            console.warn("BG: TTS不可用，无法停止播放");
            sendResponse({ error: "TTS不可用" });
          }
          break;

        case "navigateToNextChapter":
          console.log("BG: 接收到导航到下一章的请求");
          if (nextChapterInfo) {
            // 重置章节计数为0，避免自动连续播放章节数限制
            currentChapterCount = 0;
            
            // 执行导航到下一章
            navigateToNextChapter().then(() => {
              console.log("BG: 成功导航到下一章");
              if (sendResponse) {
                sendResponse({ status: "Navigation to next chapter completed" });
              }
            }).catch(error => {
              console.error("BG: 导航到下一章出错:", error);
              if (sendResponse) {
                sendResponse({ status: "Navigation error", error: error.message });
              }
            });
            
            // 返回true表示将异步发送响应
            return true;
          } else {
            console.warn("BG: 没有找到预加载的下一章信息");
            sendResponse({ status: "No preloaded next chapter information" });
          }
          break;

        case "markAutoRefreshForPlay":
          // 标记播放按钮发起的自动刷新
          if (message.tabId) {
            markAutoRefreshForPlay(message.tabId);
            sendResponse({ status: "自动刷新标记已设置" });
          } else {
            console.error("BG: 标记自动刷新失败，缺少tabId");
            sendResponse({ error: "缺少tabId参数" });
          }
          break;

        case "playURL":
          // 播放指定URL的内容
          console.log("BG: 收到播放URL请求:", message.url);
          if (!message.url) {
            sendResponse({ success: false, error: "缺少URL参数" });
            break;
          }

          handlePlayURL(message.url).then(result => {
            if (sendResponse) {
              sendResponse(result);
            }
          }).catch(error => {
            console.error("BG: 播放URL时出错:", error);
            if (sendResponse) {
              sendResponse({ success: false, error: error.message || "播放失败" });
            }
          });
          return true; // 异步响应

        // 播放当前页面功能已集成到原有的播放逻辑中

        default:
          console.log("BG: Received unknown message action:", message.action);
          sendResponse({ error: "Unknown action" });
          break;
      }

      // 不要在这里返回true，因为大多数操作已经处理了响应
      // 只有明确需要异步响应的case才会返回true
    });
  } catch (e) {
    console.error("设置消息监听器失败:", e);
  }
}

// --- 下一章识别和导航功能 ---
/**
 * 在当前阅读页面中查找下一章节链接
 * @returns {Promise<string|null>} 下一章节的URL或null
 */
async function findNextChapterInCurrentPage() {
  if (!currentReadingTabId) {
    console.error("查找下一章链接: 没有当前阅读标签页ID");
    return null;
  }
  
  return new Promise((resolve) => {
    console.log("查找下一章链接...");
    
    // 使用currentReadingTabId而非查询当前活动标签页
    chrome.scripting.executeScript({
      target: { tabId: currentReadingTabId },
      function: findNextPageLinkSimple
    }, (results) => {
      if (chrome.runtime.lastError) {
        console.error("执行查找下一章脚本时出错:", chrome.runtime.lastError);
        resolve(null);
        return;
      }
      
      if (!results || !results[0]) {
        console.warn("查找下一章脚本执行后没有返回结果");
        resolve(null);
        return;
      }
      
      console.log("找到下一章链接:", results[0].result);
      resolve(results[0].result);
    });
  });
}

/**
 * 使用简单文本匹配查找下一章/节/页链接
 * @returns {string|null} 下一页的URL或null
 */
function findNextPageLinkSimple() {
  // 按优先级排序的关键词列表（小说阅读场景）
  const prioritizedKeywords = [
    // 最常见的下一章关键词
    '下一章', 'next chapter', '下章',
    '下一节', 'next section', '下节',
    '下一页', 'next page', '下页',
    '下一篇', 'next article', '下篇',
    '下一卷', 'next volume', '下卷',

    // 符号类
    '>>>', '»', '→', '▶', '▷',
    'next', '下一个', '继续阅读',

    // 数字模式（查找比当前章节号大1的链接）
    '第.*章', 'chapter.*\\d+',

    // 通用导航
    '后一页', '翻页', '继续'
  ];
  
  // 获取所有链接
  const links = Array.from(document.querySelectorAll('a[href]'));
  console.log(`查找下一章：共找到 ${links.length} 个链接`);

  // 首先尝试智能章节号识别
  const currentTitle = document.title;
  const currentChapterMatch = currentTitle.match(/第\s*(\d+)\s*章/);

  if (currentChapterMatch) {
    const currentChapterNum = parseInt(currentChapterMatch[1]);
    const nextChapterNum = currentChapterNum + 1;
    console.log(`当前章节号: ${currentChapterNum}，查找第 ${nextChapterNum} 章`);

    // 查找下一章节号的链接
    for (const link of links) {
      const linkText = link.textContent.trim();
      const linkChapterMatch = linkText.match(/第\s*(\d+)\s*章/);

      if (linkChapterMatch) {
        const linkChapterNum = parseInt(linkChapterMatch[1]);
        if (linkChapterNum === nextChapterNum) {
          console.log(`找到下一章链接（章节号匹配）: ${linkText}`);
          return link.href;
        }
      }
    }
  }

  // 如果章节号识别失败，使用关键词匹配
  for (const keyword of prioritizedKeywords) {
    for (const link of links) {
      const text = link.textContent.trim().toLowerCase();
      const href = link.href.toLowerCase();

      // 检查链接文本和href
      if (text.includes(keyword.toLowerCase()) ||
          (keyword.length > 2 && href.includes(keyword.toLowerCase()))) {

        // 排除一些明显不是下一章的链接
        if (text.includes('上一') || text.includes('previous') ||
            text.includes('返回') || text.includes('back') ||
            text.includes('目录') || text.includes('index')) {
          continue;
        }

        console.log(`找到下一页链接: ${link.textContent} (关键词: ${keyword})`);
        return link.href;
      }
    }
  }

  // 最后尝试查找包含数字且比当前页面大的链接
  if (currentChapterMatch) {
    const currentNum = parseInt(currentChapterMatch[1]);

    for (const link of links) {
      const linkText = link.textContent.trim();
      const numbers = linkText.match(/\d+/g);

      if (numbers) {
        for (const numStr of numbers) {
          const num = parseInt(numStr);
          if (num === currentNum + 1) {
            console.log(`找到下一章链接（数字匹配）: ${linkText}`);
            return link.href;
          }
        }
      }
    }
  }

  console.log("没有找到任何匹配的下一章链接");
  return null;
}

/**
 * 预加载下一章内容
 * 该函数不会影响当前播放状态，仅在后台获取和处理下一章内容
 */
async function preloadNextChapter() {
  console.log("开始预加载下一章...");
  
  try {
    // 确保存在当前阅读标签页ID
    if (!currentReadingTabId) {
      console.error("预加载: 没有当前阅读标签页ID");
      return;
    }
    
    // 查找下一章链接
    const nextChapterUrl = await findNextChapterInCurrentPage();
    
    if (!nextChapterUrl) {
      console.log("预加载: 没有找到下一章链接");
      nextChapterInfo = null;
      sendStateUpdate(); // 发送状态更新以清除UI上的预加载信息
      return;
    }
    
    console.log(`预加载: 找到下一章链接: ${nextChapterUrl}`);
    
    // 使用currentReadingTabId而非查询当前活动标签页
    // 确保即使用户切换到其他标签页，预加载也会在原阅读页面的上下文中执行
    console.log(`预加载: 在标签页 ${currentReadingTabId} 上下文中执行`);
    
    // 使用安全的预加载方法
    console.log("预加载: 开始获取下一章内容");

    try {
      // 使用fetch获取下一章内容
      const response = await fetch(nextChapterUrl);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const html = await response.text();
      console.log("预加载: 成功获取下一章HTML，长度:", html.length);

      // 创建临时DOM来解析内容
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');

      // 使用简单的内容提取
      let title = doc.title || "下一章";
      let textContent = "";

      // 尝试提取主要内容
      const contentSelectors = [
        'article', '.article', '#article',
        '.content', '#content', '.main-content',
        '.post-content', '.entry-content',
        'main', '.main'
      ];

      let contentElement = null;
      for (const selector of contentSelectors) {
        contentElement = doc.querySelector(selector);
        if (contentElement) break;
      }

      if (contentElement) {
        textContent = contentElement.innerText || contentElement.textContent || "";
      } else {
        // 回退到body内容
        textContent = doc.body.innerText || doc.body.textContent || "";
      }

      // 清理文本内容
      textContent = textContent.trim();

      if (textContent.length > 100) {
        // 保存完整的预加载信息
        nextChapterInfo = {
          url: nextChapterUrl,
          title: title,
          textContent: textContent,
          content: contentElement ? contentElement.innerHTML : doc.body.innerHTML,
          isPreloaded: true,
          isFullyPreloaded: true,
          length: textContent.length
        };

        console.log("预加载: 成功预加载下一章内容");
        console.log("  标题:", title);
        console.log("  内容长度:", textContent.length);
      } else {
        // 内容太短，可能解析失败
        console.warn("预加载: 提取的内容太短，可能解析失败");
        nextChapterInfo = {
          url: nextChapterUrl,
          title: title,
          isPreloaded: true,
          isFullyPreloaded: false
        };
      }

    } catch (error) {
      console.error("预加载: 获取下一章内容失败:", error);
      // 保存基本信息，稍后通过导航获取
      nextChapterInfo = {
        url: nextChapterUrl,
        title: "下一章",
        isPreloaded: true,
        isFullyPreloaded: false
      };
    }

    console.log("预加载: 已保存下一章信息:", nextChapterInfo);
    sendStateUpdate();



  } catch (error) {
    console.error("预加载下一章时出错:", error);
    nextChapterInfo = null;
    sendStateUpdate(); // 确保在出错时也发送状态更新
  }
}

/**
 * 导航到下一章并开始阅读
 */
async function navigateToNextChapter() {
  console.log("开始查找下一章...");
  
  // 更新章节计数
  currentChapterCount++;
  console.log(`当前已读章节数: ${currentChapterCount}/${continuousChapters}`);
  
  // 检查是否达到用户设定的章节数上限
  if (currentChapterCount >= continuousChapters) {
    console.log(`已读完设定的 ${continuousChapters} 章，停止连续阅读`);
    readingState = 'idle';
    currentUtterance = null;
    nextChapterInfo = null; // 清除预加载信息
    sendStateUpdate();
    return;
  }
  
  // 设置状态为导航中，UI会特殊处理这个状态保持播放体验一致性
  readingState = 'navigating';
  sendStateUpdate();
  
  // 保存当前活动窗口和标签页信息（用于之后恢复）
  let activeWindowId = null;
  let activeTabId = null;
  
  try {
    // 获取当前活动窗口和标签页
    const activeWindow = await new Promise(resolve => {
      chrome.windows.getCurrent({populate: true}, window => resolve(window));
    });
    
    if (activeWindow) {
      activeWindowId = activeWindow.id;
      const activeTabs = activeWindow.tabs.filter(tab => tab.active);
      if (activeTabs.length > 0) {
        activeTabId = activeTabs[0].id;
      }
    }
    
    console.log(`当前活动标签页: ${activeTabId}, 朗读标签页: ${currentReadingTabId}`);
  } catch (e) {
    console.error("获取当前活动窗口/标签页出错:", e);
  }
  
  // 检查是否有预加载的完整内容
  if (nextChapterInfo && nextChapterInfo.isFullyPreloaded && nextChapterInfo.textContent) {
    console.log("使用预加载的完整内容直接播放，无需导航:", nextChapterInfo.title);

    // 使用预加载内容，但仍然更新当前URL（为了地址栏显示正确）
    const nextChapterUrl = nextChapterInfo.url;

    // 使用预加载的内容
    const preloadedContent = {
      title: nextChapterInfo.title,
      textContent: nextChapterInfo.textContent,
      content: nextChapterInfo.content,
      byline: nextChapterInfo.byline,
      excerpt: nextChapterInfo.excerpt,
      siteName: nextChapterInfo.siteName,
      length: nextChapterInfo.length
    };

    // 将预加载内容保存到缓存中
    tabContentCache[currentReadingTabId] = preloadedContent;

    // 直接开始阅读预加载的内容，不进行页面导航
    readingState = 'reading';

    // 使用continuousSpeakText朗读预加载的内容
    continuousSpeakText(preloadedContent.textContent, currentReadingTabId);

    // 发送完整的状态更新，确保UI更新
    sendStateUpdate(currentReadingTabId);

    // 清除当前预加载信息
    nextChapterInfo = null;

    // 开始预加载下一个章节
    setTimeout(() => {
      preloadNextChapter().catch(err => {
        console.error("预加载下一章时出错:", err);
      });
    }, 500);

    // 在播放开始后再导航页面（仅用于更新地址栏）
    setTimeout(() => {
      console.log("更新地址栏到下一章URL:", nextChapterUrl);
      chrome.tabs.update(currentReadingTabId, { url: nextChapterUrl }, (tab) => {
        if (tab) {
          console.log("地址栏已更新到下一章");
        }
      });
    }, 2000); // 等待播放稳定后再更新地址栏

    return;
  }
  
  // 如果没有完整预加载内容，回退到原有逻辑：使用预加载的链接或实时查找
  let nextChapterUrl = null;
  if (nextChapterInfo && nextChapterInfo.url) {
    console.log("使用预加载的下一章链接:", nextChapterInfo.url);
    nextChapterUrl = nextChapterInfo.url;
    // 清除预加载信息，将在新页面加载后重新预加载
    nextChapterInfo = null;
  } else {
    // 查找下一章链接
    nextChapterUrl = await findNextChapterInCurrentPage();
  }
  
  if (!nextChapterUrl) {
    console.log("没有找到下一章链接，停止连续阅读");
    readingState = 'idle';
    currentUtterance = null;
    sendStateUpdate();
    return;
  }
  
  console.log(`导航到下一章: ${nextChapterUrl}`);
  
  // 导航到下一章（在原阅读标签页中）
  chrome.tabs.update(currentReadingTabId, { url: nextChapterUrl }, (tab) => {
    if (!tab) {
      console.error("导航到下一章失败");
      readingState = 'idle';
      currentUtterance = null;
      sendStateUpdate();
      return;
    }

    // 标记这是自动章节切换，确保刷新后自动播放（但只播放一次）
    markAutoRefreshForPlay(tab.id);

    // 设置标记，表示正在等待下一章内容
    readingState = 'navigating';
    sendStateUpdate(tab.id);

    // 等待页面加载完成
    function checkIfComplete() {
      chrome.tabs.get(tab.id, (updatedTab) => {
        if (!updatedTab) {
          console.error("获取标签页信息失败");
          readingState = 'idle';
          currentUtterance = null;
          sendStateUpdate();
          return;
        }

        if (updatedTab.status === 'complete') {
          // 页面加载完成，等待内容解析
          setTimeout(() => {
            console.log("页面加载完成，等待内容解析");
            // 检查内容是否已解析
            checkIfContentParsed();
          }, 500);
        } else {
          // 继续等待
          setTimeout(checkIfComplete, 300);
        }
      });
    }

    function checkIfContentParsed() {
      if (tabContentCache[tab.id]) {
        console.log("内容已解析，开始阅读下一章");

        // 检查是否应该开始播放
        if (readingState === 'navigating' || readingState === 'loading') {
          console.log("内容已解析，开始阅读下一章");
          // 开始阅读
          readingState = 'reading';

          // 使用continuousSpeakText代替speakText，保持TTS引擎状态
          continuousSpeakText(tabContentCache[tab.id].textContent, tab.id);

          // 确保发送状态更新使UI正确更新
          sendStateUpdate(tab.id);

          // 开始预加载下一个章节
          setTimeout(() => {
            preloadNextChapter().catch(err => {
              console.error("预加载下一章时出错:", err);
            });
          }, 500);
        } else if (readingState === 'reading') {
          console.log("已在播放状态，跳过重复播放");
        } else {
          console.log("状态不匹配，当前状态:", readingState);
        }
      } else {
        console.log("等待内容解析...");
        // 继续等待内容解析
        setTimeout(checkIfContentParsed, 300);
      }
    }

    checkIfComplete();
  });
}

// --- TTS Handling ---
function speakText(text, tabId, startPosition = 0) {
  if (!isTtsAvailable) {
    console.warn("无法执行语音操作：chrome.tts API不可用");
    return;
  }

  if (!text) {
    console.error("BG: speakText 被调用但文本为空");
    readingState = 'idle';
    sendStateUpdate(tabId);
    return;
  }
  
  console.log(`BG: 开始朗读文本，标签页: ${tabId}，文本长度: ${text.length}，语速: ${currentSpeed}，起始位置: ${startPosition}`);
  
  // 初始化或更新当前朗读位置
  if (!currentUtterance) {
    currentUtterance = { position: startPosition };
  } else {
    // 更新现有的位置信息
    currentUtterance.position = startPosition;
  }
  
  // 在开始朗读后启动预加载（首次播放时）
  if (readingState !== 'reading') {
    // 确保是第一次开始播放而不是恢复播放时才触发预加载
    setTimeout(() => {
      preloadNextChapter().catch(err => {
        console.error("首次预加载下一章时出错:", err);
      });
    }, 500);
  }
  
  // 开始朗读前保存状态
  readingState = 'reading';
  currentReadingTabId = tabId;
  savePlaybackState();
  
  chrome.tts.speak(text, {
    voiceName: currentVoice, // 使用选择的语音
    rate: currentSpeed,
    onEvent: function (event) {
      // 确保有事件类型
      if (!event || !event.type) {
        console.warn("BG: 收到无效的TTS事件");
        return;
      }
      
      console.log("BG: TTS事件:", event.type, "字符索引:", event.charIndex);
      
      switch (event.type) {
        case 'start':
          // 朗读开始时更新位置
          currentUtterance = { position: startPosition + (event.charIndex || 0) };
          console.log("BG: 朗读开始于位置:", currentUtterance.position);

          // 开始播放时长计时器
          startPlaybackDurationTimer();
          break;
          
        case 'word':
        case 'sentence':
          // 朗读过程中更新位置（如果有字符索引）
          if (typeof event.charIndex === 'number') {
            currentUtterance.position = startPosition + event.charIndex;
            // 定期保存状态，但不要太频繁
            if (Math.random() < 0.05) { // 5%的概率保存，避免过于频繁
              savePlaybackState();
            }
          }
          break;
          
        case 'end':
          console.log('🔚 TTS朗读完成');
          console.log('📍 最终朗读位置:', currentUtterance.position);
          console.log('📊 连续阅读状态:', {
            continuousReading: continuousReading,
            currentChapterCount: currentChapterCount,
            continuousChapters: continuousChapters,
            hasNextChapterInfo: !!nextChapterInfo
          });

          // 实现连续阅读功能
          if (continuousReading && currentChapterCount < continuousChapters) {
            console.log(`🔄 连续阅读已启用，当前章节 ${currentChapterCount}/${continuousChapters}，开始查找下一章`);

            // 不要在这里重置状态，而是在navigateToNextChapter中处理
            readingState = 'navigating'; // 使用临时状态表示正在导航
            savePlaybackState(); // 保存导航状态
            sendStateUpdate(tabId);

            // 延迟一点时间再导航，确保当前播放完全结束
            setTimeout(() => {
              console.log("🚀 开始导航到下一章...");
              navigateToNextChapter().catch(err => {
                console.error("❌ 导航到下一章时出错:", err);
                readingState = 'idle';
                currentUtterance = null;
                sendStateUpdate();
              });
            }, 1000);
          } else {
            if (!continuousReading) {
              console.log("⏹️ 播放结束，连续阅读未启用");
            } else {
              console.log(`⏹️ 播放结束，已达到章节上限 ${currentChapterCount}/${continuousChapters}`);
            }
            readingState = 'idle';
            currentUtterance = null;
            currentChapterCount = 0; // 非连续阅读模式下重置章节计数
            nextChapterInfo = null; // 清除预加载信息
            savePlaybackState(); // 保存状态
            sendStateUpdate(tabId);
          }
          break;
          
        case 'interrupted':
        case 'cancelled':
          console.log('BG: TTS被中断或取消');
          
          // 保留当前位置用于恢复
          if (readingState !== 'paused') {
            // 如果不是暂停状态（如停止），则重置状态
            readingState = 'idle';
            currentUtterance = null;
            currentChapterCount = 0; // 重置章节计数
            savePlaybackState(); // 保存状态
          }
          
          sendStateUpdate(tabId);
          break;
          
        case 'error':
          console.error('BG: TTS错误:', event.errorMessage);
          readingState = 'idle'; // 错误时设置状态为空闲
          currentUtterance = null;
          savePlaybackState(); // 保存状态
          sendStateUpdate(tabId);
          break;
          
        default:
          // 忽略其他事件类型
          break;
      }
    }
  });
}

/**
 * 连续朗读文本，不重新初始化TTS引擎
 * 在章节切换时调用该函数，保持TTS引擎状态
 */
function continuousSpeakText(text, tabId, startPosition = 0) {
  if (!isTtsAvailable) {
    console.warn("无法执行语音操作：chrome.tts API不可用");
    return;
  }

  if (!text) {
    console.error("BG: continuousSpeakText 被调用但文本为空");
    readingState = 'idle';
    savePlaybackState(); // 保存状态
    sendStateUpdate(tabId);
    return;
  }
  
  console.log(`BG: 无缝切换朗读文本，标签页: ${tabId}，文本长度: ${text.length}，语速: ${currentSpeed}，起始位置: ${startPosition}`);
  
  // 初始化或更新当前朗读位置
  if (!currentUtterance) {
    currentUtterance = { position: startPosition };
  } else {
    // 更新现有的位置信息
    currentUtterance.position = startPosition;
  }
  
  // 先停止当前朗读，但保留引擎状态
  chrome.tts.stop();
  
  // 更新状态
  readingState = 'reading';
  currentReadingTabId = tabId;
  savePlaybackState(); // 保存状态
  
  // 直接使用已初始化的TTS引擎开始新文本朗读
  chrome.tts.speak(text, {
    voiceName: currentVoice,
    rate: currentSpeed,
    onEvent: function (event) {
      // 确保有事件类型
      if (!event || !event.type) {
        console.warn("BG: 收到无效的TTS事件");
        return;
      }
      
      console.log("BG: TTS事件:", event.type, "字符索引:", event.charIndex);
      
      switch (event.type) {
        case 'start':
          // 朗读开始时更新位置
          currentUtterance = { position: startPosition + (event.charIndex || 0) };
          console.log("BG: 朗读开始于位置:", currentUtterance.position);
          // 确保更新状态为朗读中
          readingState = 'reading';
          savePlaybackState(); // 保存状态
          sendStateUpdate(tabId);

          // 开始播放时长计时器（如果还没有开始）
          if (!playbackTimer) {
            startPlaybackDurationTimer();
          }
          break;
          
        case 'word':
        case 'sentence':
          // 朗读过程中更新位置（如果有字符索引）
          if (typeof event.charIndex === 'number') {
            currentUtterance.position = startPosition + event.charIndex;
            // 定期保存状态，但不要太频繁
            if (Math.random() < 0.05) { // 5%的概率保存，避免过于频繁
              savePlaybackState();
            }
          }
          break;
          
        case 'end':
          console.log('🔚 TTS朗读完成（continuousSpeakText）');
          console.log('📍 最终朗读位置:', currentUtterance.position);
          console.log('📊 连续阅读状态:', {
            continuousReading: continuousReading,
            currentChapterCount: currentChapterCount,
            continuousChapters: continuousChapters,
            hasNextChapterInfo: !!nextChapterInfo
          });

          // 实现连续阅读功能
          if (continuousReading && currentChapterCount < continuousChapters) {
            console.log(`🔄 连续阅读已启用，当前章节 ${currentChapterCount}/${continuousChapters}，开始查找下一章`);

            // 不要在这里重置状态，而是在navigateToNextChapter中处理
            readingState = 'navigating'; // 使用临时状态表示正在导航
            savePlaybackState(); // 保存导航状态
            sendStateUpdate(tabId);

            // 延迟一点时间再导航，确保当前播放完全结束
            setTimeout(() => {
              console.log("🚀 开始导航到下一章...");
              navigateToNextChapter().catch(err => {
                console.error("❌ 导航到下一章时出错:", err);
                readingState = 'idle';
                currentUtterance = null;
                sendStateUpdate();
              });
            }, 1000);
          } else {
            if (!continuousReading) {
              console.log("⏹️ 播放结束，连续阅读未启用");
            } else {
              console.log(`⏹️ 播放结束，已达到章节上限 ${currentChapterCount}/${continuousChapters}`);
            }
            readingState = 'idle';
            currentUtterance = null;
            currentChapterCount = 0; // 非连续阅读模式下重置章节计数
            nextChapterInfo = null; // 清除预加载信息
            savePlaybackState(); // 保存状态
            sendStateUpdate(tabId);
          }
          break;
          
        case 'interrupted':
        case 'cancelled':
          console.log('BG: TTS被中断或取消');
          
          // 保留当前位置用于恢复
          if (readingState !== 'paused') {
            // 如果不是暂停状态（如停止），则重置状态
            readingState = 'idle';
            currentUtterance = null;
            currentChapterCount = 0; // 重置章节计数
            savePlaybackState(); // 保存状态
          }
          
          sendStateUpdate(tabId);
          break;
          
        case 'error':
          console.error('BG: TTS错误:', event.errorMessage);
          readingState = 'idle'; // 错误时设置状态为空闲
          currentUtterance = null;
          savePlaybackState(); // 保存状态
          sendStateUpdate(tabId);
          break;
          
        default:
          // 忽略其他事件类型
          break;
      }
    }
  });
}

// --- Side Panel Opener ---
// 核心点击事件处理
if (isActionAvailable) {
  try {
    chrome.action.onClicked.addListener((tab) => {
      console.log('BG: Extension icon clicked - tab info:', {
          id: tab.id,
          windowId: tab.windowId,
          url: tab.url
      });
      
      if (!tab?.windowId) {
          console.error('BG: Invalid tab - missing windowId');
          return;
      }

      // 只有当sidePanel API可用时才尝试打开
      if (isSidePanelAvailable) {
        console.log("BG: Attempting to open side panel...");
        chrome.sidePanel.open({windowId: tab.windowId})
            .then(() => {
                console.log("BG: Side panel opened successfully");
                currentReadingTabId = tab.id;
                sendStateUpdate(tab.id);
                
                // Double check panel state
                try {
                  chrome.sidePanel.getOptions((options) => {
                    console.log("BG: Side panel options:", options);
                  });
                  return true; // 继续链式操作
                } catch (err) {
                  console.warn("BG: Error getting options:", err);
                  return true; // 继续链式操作
                }
            })
            .catch(err => {
                console.error("BG: Failed to open side panel:", err);
                // Fallback to alternative opening method if needed
                if (isTabsAvailable) {
                  chrome.windows.get(tab.windowId, {populate: true}, (win) => {
                      console.log("BG: Window info:", win);
                  });
                }
            });
      } else {
        console.warn("BG: sidePanel API not available, cannot open side panel");
        // 尝试通知用户
        if (isTabsAvailable) {
          chrome.tabs.sendMessage(tab.id, { action: 'showUnsupportedNotification' })
            .catch(err => console.error("Could not send notification message:", err));
        }
      }
    });
  } catch (e) {
    console.error("设置action.onClicked监听器失败:", e);
  }
}

// Listen for side panel visibility changes, only if API is available
if (isSidePanelAvailable) {
  try {
    // 使用可选链接和更强的错误处理
    chrome.sidePanel.onPanelHidden?.addListener(({ windowId }) => {
        console.log("BG: Side panel hidden for window:", windowId, "当前状态:", readingState);
        // 恢复在侧边栏隐藏时停止播放的代码
        // 无条件停止播放，不依赖于readingState的状态
        if (isTtsAvailable) {
            console.log("BG: Stopping playback due to panel close");
            stopTTSCompletely("panel_hidden_event");
        }
    });
    
    // 为了防止onPanelHidden事件不触发，添加一个备用的检测方法
    const checkSidePanelInterval = setInterval(() => {
      if ((readingState === 'reading' || readingState === 'paused' || readingState === 'waiting_refresh') && isTtsAvailable) {
        // 检查侧边栏是否可见
        try {
          // 方法1: 使用API检查
          if (chrome.sidePanel && chrome.sidePanel.getOptions) {
            chrome.sidePanel.getOptions((options) => {
              if (!options || !options.open) {
                console.log("BG: Side panel detected closed via polling, 当前状态:", readingState);
                // 恢复停止播放的代码
                stopTTSCompletely("polling");
              }
            });
          }
          
          // 方法2: 检查本地存储中的关闭标记
          if (chrome.storage && chrome.storage.local) {
            // 创建一个检查器
            const checkLocalStorageClosingMarker = (tabId) => {
              if (!tabId) return;
              
              try {
                chrome.tabs.executeScript(tabId, {
                  code: `
                    const closing = localStorage.getItem('sidepanel_closing');
                    const timestamp = localStorage.getItem('sidepanel_closing_timestamp');
                    const state = localStorage.getItem('sidepanel_closing_state');
                    ({closing, timestamp, state})
                  `
                }, (results) => {
                  if (chrome.runtime.lastError) {
                    // 忽略执行错误
                    return;
                  }
                  
                  if (results && results[0] && results[0].closing === 'true') {
                    // 检查时间戳，确保是最近的关闭事件(5秒内)
                    const closingTime = parseInt(results[0].timestamp || '0');
                    if (Date.now() - closingTime < 5000) {
                      console.log("BG: 通过localStorage检测到侧边栏关闭标记");
                      stopTTSCompletely("localStorage_marker");
                      
                      // 清除标记，避免重复处理
                      chrome.tabs.executeScript(tabId, {
                        code: `
                          localStorage.removeItem('sidepanel_closing');
                          localStorage.removeItem('sidepanel_closing_timestamp');
                          localStorage.removeItem('sidepanel_closing_state');
                        `
                      });
                    }
                  }
                });
              } catch (e) {
                console.error("检查关闭标记时出错:", e);
              }
            };
            
            // 检查当前阅读标签页
            if (currentReadingTabId) {
              checkLocalStorageClosingMarker(currentReadingTabId);
            }
          }
        } catch (e) {
          console.warn("BG: Error during side panel check:", e);
        }
      }
    }, 1000); // 每秒检查一次
    
    // 清理定时器
    chrome.runtime.onSuspend?.addListener(() => {
      clearInterval(checkSidePanelInterval);
    });
  } catch (e) {
    console.error("设置sidePanel.onPanelHidden监听器失败:", e);
  }
}

// 修改断开连接监听器
chrome.runtime.onConnect.addListener((port) => {
  console.log("BG: Connection established with:", port.name);
  
  // 记录活跃的连接
  const isSidepanel = port.name.includes('sidepanel');
  if (isSidepanel) {
    console.log("BG: Sidepanel connection registered");
  }
  
  // 处理来自连接的消息
  port.onMessage.addListener((message) => {
    if (message.type === 'heartbeat') {
      // 收到心跳消息，连接仍然活跃
      console.log("BG: Heartbeat received from port:", port.name);
      
      // 如果需要，可以发送当前状态回去
      if (isSidepanel) {
        port.postMessage({
          type: 'status',
          readingState,
          currentSpeed,
          currentVoice,
          continuousReading,
          readingDuration
        });
      }
    }
  });
  
  port.onDisconnect.addListener(() => {
    console.log("BG: Connection disconnected:", port.name, "当前状态:", readingState);
    // 如果这是一个侧边栏连接，确保停止朗读
    if (isSidepanel) {
      if (readingState === 'reading' || readingState === 'paused' || readingState === 'waiting_refresh') {
        console.log("BG: Sidepanel connection lost, stopping playback");
        if (isTtsAvailable) {
          stopTTSCompletely("connection_lost");
        }
      }
    }
  });
});

// 添加一个完全停止TTS的函数，集中处理各种停止场景
function stopTTSCompletely(source = "unknown") {
  if (!isTtsAvailable) return;
  
  console.log(`BG: 完全停止TTS (来源: ${source}), 当前状态: ${readingState}`);
  
  // 立即重置状态，避免状态不同步
  const previousState = readingState;
  readingState = 'idle';
  
  // 使用多次尝试确保停止
  try {
    // 首次尝试，高优先级立即执行
    chrome.tts.stop(() => {
      console.log(`BG: 第一次TTS停止完成 (来源: ${source})`);
      
      // 检查是否真的停止了
      chrome.tts.isSpeaking((speaking) => {
        if (speaking) {
          console.log(`BG: 第一次停止后仍在播放，进行第二次尝试`);
          // 第二次尝试，使用可选的回调
          chrome.tts.stop(() => {
            console.log(`BG: 第二次TTS停止完成 (来源: ${source})`);
            
            // 最后一次检查
            chrome.tts.isSpeaking((stillSpeaking) => {
              if (stillSpeaking) {
                console.warn(`BG: 多次停止后仍在播放，强制重置`);
                
                // 第三次尝试，更激进的方式
                setTimeout(() => {
                  chrome.tts.stop();
                  // 最后的检查
                  setTimeout(() => {
                    chrome.tts.isSpeaking((finalCheck) => {
                      if (finalCheck) {
                        console.error(`BG: 所有停止尝试失败，将重新加载TTS引擎`);
                        // 尝试重新初始化TTS引擎
                        chrome.tts.stop();
                        isTtsAvailable = false;
                        setTimeout(() => {
                          initTTS();
                        }, 500);
                      }
                    });
                  }, 200);
                }, 100);
              }
            });
          });
        } else {
          console.log(`BG: TTS已成功停止 (来源: ${source})`);
        }
      });
    });
  } catch (e) {
    console.error(`BG: 停止TTS出错 (${source}):`, e);
    // 即使出错也继续执行后续操作
    // 强制停止尝试
    setTimeout(() => {
      chrome.tts.stop();
    }, 0);
  }
  
  // 停止播放时长计时器
  stopPlaybackDurationTimer();

  // 在状态重置之后尝试清理资源
  currentUtterance = null;
  currentChapterCount = 0;
  nextChapterInfo = null;
  
  // 保存状态
  savePlaybackState();
  
  // 记录到storage用于调试
  try {
    chrome.storage.local.set({
      last_tts_stop: {
        timestamp: Date.now(),
        source: source,
        previousState: previousState,
        success: true
      }
    });
  } catch (e) {
    console.error(`BG: 保存停止记录失败 (${source}):`, e);
  }
  
  // 同步状态变化到UI（使用setTimeout确保先执行状态更改）
  setTimeout(() => {
    sendStateUpdate();
  }, 10);
  
  // 额外的状态检查
  setTimeout(() => {
    if (readingState !== 'idle') {
      console.warn(`BG: 停止后状态不一致，强制设置为idle`);
      readingState = 'idle';
      sendStateUpdate();
    }
  }, 1000);
}

// 在background.js中添加标签页关闭监听器
if (isTabsAvailable) {
  chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
    console.log("BG: Tab closed:", tabId);
    // 如果关闭的标签页是当前正在朗读的标签页
    if (tabId === currentReadingTabId) {
      console.log("BG: Active reading tab closed, stopping playback");
      if (isTtsAvailable) {
        stopTTSCompletely("tab_closed");
        currentReadingTabId = null;
      }
    }
  });
  
  // 添加标签页更新监听器，处理用户刷新页面的情况
  chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // 仅当页面完成加载时处理
    if (changeInfo.status === 'complete') {
      console.log(`BG: 标签页 ${tabId} 已更新，状态: ${readingState}`);
      
      // 如果是当前阅读标签页，仅记录日志，不改变状态
      if (tabId === currentReadingTabId) {
        console.log(`BG: 当前阅读标签页 ${tabId} 已更新，维持当前状态: ${readingState}`);
        
        // 纯日志记录，不影响功能
        if (readingState === 'reading' || readingState === 'paused') {
          console.log(`BG: 阅读标签页已刷新，当前状态: ${readingState}，保持不变`);
        }
      } else {
        // 非阅读标签页的刷新不影响朗读
        console.log(`BG: 非阅读标签页 ${tabId} 被刷新/更新，不影响当前朗读`);
      }
    }
  });
  
  // 添加标签页激活监听器
  chrome.tabs.onActivated.addListener((activeInfo) => {
    console.log("BG: Tab activated:", activeInfo.tabId);
    
    // 不阻止朗读，但记录当前标签页是否是阅读标签页
    const isReadingTab = activeInfo.tabId === currentReadingTabId;
    console.log(`BG: 当前激活的标签页${isReadingTab ? '是' : '不是'}阅读标签页`);
    
    // 只有切换到阅读标签页时，才考虑更新UI，但也不是必须的
    if (isReadingTab && (readingState === 'reading' || readingState === 'paused')) {
      console.log("BG: 用户返回到阅读标签页，但保持当前状态:", readingState);
      // 不主动更新UI状态，避免干扰正在进行的朗读
    }
  });
  
  // 监听窗口关闭
  if (chrome.windows) {
    chrome.windows.onRemoved.addListener((windowId) => {
      console.log("BG: Window closed:", windowId);
      // 检查是否有任何朗读正在进行
      if (readingState === 'reading' || readingState === 'paused' || readingState === 'waiting_refresh') {
        console.log("BG: Window closed while reading, stopping playback");
        if (isTtsAvailable) {
          stopTTSCompletely("window_closed");
          currentReadingTabId = null;
        }
      }
    });
    
    // 添加窗口焦点变化监听，但不做任何停止播放的操作
    chrome.windows.onFocusChanged.addListener((windowId) => {
      console.log("BG: Window focus changed:", windowId);
      // 即使是 WINDOW_ID_NONE (-1) 也不停止播放，让它继续在后台播放
    });
  }
}

// 获取当前朗读位置的函数
function getCurrentUtterancePosition(callback) {
  if (!isTtsAvailable) {
    callback(0);
    return;
  }
  
  try {
    chrome.tts.isSpeaking((speaking) => {
      if (!speaking || !currentUtterance) {
        // 如果没有朗读中，返回默认位置0或之前保存的位置
        const position = currentUtterance ? (currentUtterance.resumePosition || 0) : 0;
        callback(position);
        return;
      }
      
      // 尝试获取当前位置
      // 注意：某些Chrome版本可能不支持精确的位置，这种情况下返回估计的位置
      if (chrome.tts.getPositionCurrent) {
        try {
          chrome.tts.getPositionCurrent((position) => {
            if (position !== undefined && position !== null) {
              callback(position);
            } else {
              // 如果无法获取精确位置，使用估计的位置
              const estimatedPosition = currentUtterance.resumePosition || 0;
              callback(estimatedPosition);
            }
          });
        } catch (e) {
          console.warn("BG: 获取精确位置失败，使用估计位置:", e);
          const estimatedPosition = currentUtterance.resumePosition || 0;
          callback(estimatedPosition);
        }
      } else {
        // 如果API不可用，使用估计的位置
        const estimatedPosition = currentUtterance.resumePosition || 0;
        callback(estimatedPosition);
      }
    });
  } catch (e) {
    console.error("BG: 获取当前朗读位置错误:", e);
    // 出错时返回默认值0
    callback(0);
  }
}

// 标记播放按钮发起的自动刷新
function markAutoRefreshForPlay(tabId) {
  console.log(`BG: 标记播放按钮自动刷新，标签页 ${tabId}`);
  // 使用chrome.storage.session存储标记
  chrome.storage.session.set({
    'autoRefreshForPlay': tabId,
    'autoRefreshOrigin': {
      tabId: tabId,
      timestamp: Date.now(),
      state: readingState,
      position: currentUtterance ? currentUtterance.position : 0
    }
  });
}

// 在background.js末尾，添加定期检查机制
// 设置一个定期检查，确保在侧边栏关闭时能正确停止TTS
function setupPeriodicalChecks() {
  // 每5秒检查一次
  setInterval(() => {
    // 只在朗读或暂停状态时检查
    if (readingState === 'reading' || readingState === 'paused') {
      console.log("定期检查 - 当前状态:", readingState);
      
      // 检查侧边栏是否已关闭
      try {
        // 尝试检查localStorage中的关闭标记
        if (typeof localStorage !== 'undefined' && localStorage.getItem('sidepanel_closing') === 'true') {
          const closeTimestamp = parseInt(localStorage.getItem('sidepanel_closing_timestamp') || '0');
          const now = Date.now();
          
          // 如果关闭标记是最近10秒内设置的
          if (closeTimestamp > 0 && (now - closeTimestamp) < 10000) {
            console.log(`BG: 检测到侧边栏关闭标记 (${now - closeTimestamp}ms前)，停止TTS`);
            // 停止TTS
            stopTTSCompletely("periodic_check_closing_marker");
            
            // 清理标记
            localStorage.removeItem('sidepanel_closing');
            localStorage.removeItem('sidepanel_closing_timestamp');
            localStorage.removeItem('sidepanel_closing_state');
          }
        }
      } catch (e) {
        console.error("BG: 检查侧边栏关闭标记时出错:", e);
      }
      
      // 验证TTS是否真的在播放
      try {
        chrome.tts.isSpeaking((speaking) => {
          if (readingState === 'reading' && !speaking) {
            console.log("BG: 状态显示正在朗读，但TTS实际没有播放，重置状态");
            stopTTSCompletely("status_validation");
          } else if (readingState !== 'reading' && speaking) {
            console.log("BG: 状态显示不在朗读，但TTS仍在播放，停止TTS");
            chrome.tts.stop();
          }
        });
      } catch (e) {
        console.error("BG: 验证TTS状态时出错:", e);
      }
    }
  }, 5000);
}

// 在适当的地方调用设置函数（如初始化时）
setupPeriodicalChecks();

console.log("Service Worker setup completed!");

// ===== 播放时长控制功能 =====

/**
 * 开始播放时长计时器
 */
function startPlaybackDurationTimer() {
  // 清除现有计时器
  if (playbackTimer) {
    clearTimeout(playbackTimer);
    playbackTimer = null;
  }

  // 记录播放开始时间
  playbackStartTime = Date.now();

  console.log(`BG: 开始播放时长计时器，设定时长: ${readingDuration}秒 (${Math.floor(readingDuration/60)}分钟)`);

  // 设置定时器，到达设定时长后自动停止
  playbackTimer = setTimeout(() => {
    console.log(`BG: 播放时长已达到设定的 ${Math.floor(readingDuration/60)} 分钟，自动停止播放`);

    // 自动停止播放
    if (isTtsAvailable && (readingState === 'reading' || readingState === 'paused')) {
      stopTTSCompletely("duration_limit_reached");

      // 发送时长到达的通知到UI
      sendStateUpdate(currentReadingTabId, {
        durationReached: true,
        message: `播放时长已达到设定的 ${Math.floor(readingDuration/60)} 分钟，已自动停止`
      });
    }

    playbackTimer = null;
    playbackStartTime = null;
  }, readingDuration * 1000);
}

/**
 * 停止播放时长计时器
 */
function stopPlaybackDurationTimer() {
  if (playbackTimer) {
    clearTimeout(playbackTimer);
    playbackTimer = null;
    console.log("BG: 播放时长计时器已停止");
  }
  playbackStartTime = null;
}

/**
 * 暂停播放时长计时器
 */
function pausePlaybackDurationTimer() {
  if (playbackTimer && playbackStartTime) {
    // 计算已经过的时间
    const elapsedTime = Math.floor((Date.now() - playbackStartTime) / 1000);
    const remainingTime = readingDuration - elapsedTime;

    console.log(`BG: 暂停播放时长计时器，已播放: ${elapsedTime}秒，剩余: ${remainingTime}秒`);

    // 清除当前计时器
    clearTimeout(playbackTimer);
    playbackTimer = null;

    // 保存剩余时间
    readingDuration = Math.max(0, remainingTime);
  }
}

/**
 * 恢复播放时长计时器
 */
function resumePlaybackDurationTimer() {
  if (readingDuration > 0) {
    console.log(`BG: 恢复播放时长计时器，剩余时长: ${readingDuration}秒`);
    startPlaybackDurationTimer();
  } else {
    console.log("BG: 播放时长已用完，无法恢复计时器");
    if (isTtsAvailable && readingState === 'reading') {
      stopTTSCompletely("duration_exhausted");
    }
  }
}

/**
 * 获取剩余播放时长（秒）
 */
function getRemainingPlaybackTime() {
  if (!playbackStartTime || !playbackTimer) {
    return readingDuration;
  }

  const elapsedTime = Math.floor((Date.now() - playbackStartTime) / 1000);
  return Math.max(0, readingDuration - elapsedTime);
}

// ===== 播放历史记录管理 =====

// 添加到播放历史记录
async function addToPlaybackHistory(article, tabId) {
  try {
    // 获取标签页信息
    const tab = await chrome.tabs.get(tabId);
    if (!tab) {
      console.warn("BG: 无法获取标签页信息，跳过历史记录添加");
      return;
    }

    // 构建历史记录项
    const historyItem = {
      title: article.title || tab.title || "未知标题",
      url: tab.url,
      timestamp: Date.now(),
      chapter: extractChapterInfo(article.title, tab.title),
      siteName: article.siteName || extractSiteName(tab.url),
      source: 'current_page' // 默认为当前页面
    };

    // 提取小说名称（去除章节信息）
    historyItem.novelName = extractNovelName(historyItem.title);

    console.log("BG: 添加播放历史记录:", historyItem);

    // 获取现有历史记录
    const result = await chrome.storage.local.get(['playbackHistory']);
    let playbackHistory = result.playbackHistory || [];

    // 按小说进行去重：移除同一部小说的旧记录
    if (historyItem.novelName) {
      playbackHistory = playbackHistory.filter(h => {
        const existingNovelName = h.novelName || extractNovelName(h.title);
        return existingNovelName !== historyItem.novelName;
      });
      console.log(`BG: 移除小说"${historyItem.novelName}"的旧记录`);
    } else {
      // 如果无法提取小说名称，按URL去重（保持原有逻辑）
      const existingIndex = playbackHistory.findIndex(h => h.url === historyItem.url);
      if (existingIndex !== -1) {
        playbackHistory.splice(existingIndex, 1);
        console.log("BG: 移除相同URL的旧记录");
      }
    }

    // 添加新记录到开头
    playbackHistory.unshift(historyItem);
    console.log("BG: 添加新的历史记录");

    // 限制历史记录数量
    const MAX_HISTORY_ITEMS = 20;
    if (playbackHistory.length > MAX_HISTORY_ITEMS) {
      playbackHistory = playbackHistory.slice(0, MAX_HISTORY_ITEMS);
    }

    // 保存历史记录
    await chrome.storage.local.set({ playbackHistory: playbackHistory });

    // 通知侧边栏更新历史记录显示
    notifyHistoryUpdate();

  } catch (error) {
    console.error("BG: 添加播放历史记录失败:", error);
  }
}

// 为URL播放添加历史记录
async function addURLPlaybackHistory(url, article) {
  try {
    const historyItem = {
      title: article.title || "未知标题",
      url: url,
      timestamp: Date.now(),
      chapter: extractChapterInfo(article.title),
      siteName: article.siteName || extractSiteName(url),
      source: 'url_input' // 标记为URL输入
    };

    // 提取小说名称（去除章节信息）
    historyItem.novelName = extractNovelName(historyItem.title);

    console.log("BG: 添加URL播放历史记录:", historyItem);

    // 获取现有历史记录
    const result = await chrome.storage.local.get(['playbackHistory']);
    let playbackHistory = result.playbackHistory || [];

    // 按小说进行去重：移除同一部小说的旧记录
    if (historyItem.novelName) {
      playbackHistory = playbackHistory.filter(h => {
        const existingNovelName = h.novelName || extractNovelName(h.title);
        return existingNovelName !== historyItem.novelName;
      });
      console.log(`BG: 移除小说"${historyItem.novelName}"的旧记录`);
    } else {
      // 如果无法提取小说名称，按URL去重（保持原有逻辑）
      const existingIndex = playbackHistory.findIndex(h => h.url === historyItem.url);
      if (existingIndex !== -1) {
        playbackHistory.splice(existingIndex, 1);
        console.log("BG: 移除相同URL的旧记录");
      }
    }

    // 添加新记录到开头
    playbackHistory.unshift(historyItem);

    // 限制历史记录数量
    const MAX_HISTORY_ITEMS = 20;
    if (playbackHistory.length > MAX_HISTORY_ITEMS) {
      playbackHistory = playbackHistory.slice(0, MAX_HISTORY_ITEMS);
    }

    // 保存历史记录
    await chrome.storage.local.set({ playbackHistory: playbackHistory });

    // 通知侧边栏更新历史记录显示
    notifyHistoryUpdate();

  } catch (error) {
    console.error("BG: 添加URL播放历史记录失败:", error);
  }
}

// 提取章节信息
function extractChapterInfo(articleTitle, pageTitle) {
  const title = articleTitle || pageTitle || "";

  // 常见的章节模式
  const chapterPatterns = [
    /第\s*(\d+)\s*章/,
    /第\s*([一二三四五六七八九十百千万]+)\s*章/,
    /Chapter\s*(\d+)/i,
    /第\s*(\d+)\s*节/,
    /第\s*(\d+)\s*回/,
    /第\s*(\d+)\s*话/,
    /(\d+)\s*章/,
    /章节\s*(\d+)/
  ];

  for (const pattern of chapterPatterns) {
    const match = title.match(pattern);
    if (match) {
      return `第${match[1]}章`;
    }
  }

  return null;
}

// 提取小说名称（去除章节信息）
function extractNovelName(title) {
  if (!title) return null;

  // 常见的章节分隔模式
  const separatorPatterns = [
    // "小说名 第X章 章节标题" 格式
    /^(.+?)\s*第\s*\d+\s*章/,
    /^(.+?)\s*第\s*[一二三四五六七八九十百千万]+\s*章/,
    /^(.+?)\s*Chapter\s*\d+/i,
    /^(.+?)\s*第\s*\d+\s*节/,
    /^(.+?)\s*第\s*\d+\s*回/,
    /^(.+?)\s*第\s*\d+\s*话/,
    /^(.+?)\s*\d+\s*章/,
    /^(.+?)\s*章节\s*\d+/,

    // "小说名-第X章" 格式
    /^(.+?)\s*[-–—]\s*第\s*\d+\s*章/,
    /^(.+?)\s*[-–—]\s*Chapter\s*\d+/i,

    // "小说名：第X章" 格式
    /^(.+?)\s*[：:]\s*第\s*\d+\s*章/,
    /^(.+?)\s*[：:]\s*Chapter\s*\d+/i,

    // "小说名_第X章" 格式
    /^(.+?)\s*[_]\s*第\s*\d+\s*章/,

    // "小说名 (第X章)" 格式
    /^(.+?)\s*\(\s*第\s*\d+\s*章/,
    /^(.+?)\s*\[\s*第\s*\d+\s*章/,

    // "小说名 | 第X章" 格式
    /^(.+?)\s*[|｜]\s*第\s*\d+\s*章/
  ];

  for (const pattern of separatorPatterns) {
    const match = title.match(pattern);
    if (match && match[1]) {
      const novelName = match[1].trim();
      // 确保提取的小说名不为空且有意义（至少2个字符）
      if (novelName.length >= 2) {
        console.log(`提取小说名称: "${title}" -> "${novelName}"`);
        return novelName;
      }
    }
  }

  // 如果没有匹配到章节模式，检查是否包含常见的小说网站标识
  const sitePatterns = [
    /^(.+?)\s*[-–—]\s*.+$/,  // "小说名 - 网站名" 格式
    /^(.+?)\s*[|｜]\s*.+$/,   // "小说名 | 网站名" 格式
    /^(.+?)\s*_\s*.+$/       // "小说名_网站名" 格式
  ];

  for (const pattern of sitePatterns) {
    const match = title.match(pattern);
    if (match && match[1]) {
      const novelName = match[1].trim();
      if (novelName.length >= 2) {
        console.log(`通过网站分隔符提取小说名称: "${title}" -> "${novelName}"`);
        return novelName;
      }
    }
  }

  // 如果都没有匹配，返回原标题（但限制长度）
  const cleanTitle = title.trim();
  if (cleanTitle.length > 30) {
    // 如果标题太长，截取前30个字符作为小说名
    const truncated = cleanTitle.substring(0, 30);
    console.log(`标题过长，截取作为小说名称: "${title}" -> "${truncated}"`);
    return truncated;
  }

  console.log(`无法提取小说名称，使用原标题: "${title}"`);
  return cleanTitle || null;
}

// 提取站点名称
function extractSiteName(url) {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;

    // 移除www前缀
    const siteName = hostname.replace(/^www\./, '');

    // 提取主域名
    const parts = siteName.split('.');
    if (parts.length >= 2) {
      return parts[parts.length - 2];
    }

    return siteName;
  } catch (error) {
    return "未知站点";
  }
}

// 通知侧边栏更新历史记录
function notifyHistoryUpdate() {
  // 向所有侧边栏发送历史记录更新通知
  chrome.runtime.sendMessage({
    action: 'historyUpdated'
  }).catch(error => {
    // 忽略错误，因为侧边栏可能没有打开
    console.log("BG: 历史记录更新通知发送失败（侧边栏可能未打开）");
  });
}

// ===== URL播放功能 =====

// 处理播放URL请求
async function handlePlayURL(url) {
  console.log("BG: 开始处理播放URL请求:", url);

  try {
    // 验证URL格式
    const urlObj = new URL(url);
    if (urlObj.protocol !== 'http:' && urlObj.protocol !== 'https:') {
      throw new Error("不支持的URL协议，仅支持HTTP和HTTPS");
    }

    // 停止当前播放
    if (readingState === 'reading' || readingState === 'paused') {
      console.log("BG: 停止当前播放以开始新的URL播放");
      stopTTSCompletely("play_url");
    }

    // 创建新标签页或使用现有标签页
    let targetTab;
    try {
      // 尝试在新标签页中打开URL
      targetTab = await chrome.tabs.create({
        url: url,
        active: false // 不激活标签页，在后台打开
      });
      console.log("BG: 在新标签页中打开URL:", targetTab.id);
    } catch (error) {
      console.error("BG: 创建新标签页失败:", error);
      throw new Error("无法打开网页");
    }

    // 等待页面加载完成
    await waitForTabLoad(targetTab.id);

    // 注入内容脚本并解析页面
    const parseResult = await parseTabContent(targetTab.id);

    if (!parseResult.success) {
      // 关闭创建的标签页
      try {
        await chrome.tabs.remove(targetTab.id);
      } catch (e) {
        console.warn("BG: 清理标签页失败:", e);
      }
      throw new Error(parseResult.error || "无法解析页面内容");
    }

    // 开始播放
    readingState = 'reading';
    currentReadingTabId = targetTab.id;

    // 使用解析的内容开始朗读
    const article = tabContentCache[targetTab.id];
    if (article && article.textContent) {
      // 添加到URL播放历史记录
      await addURLPlaybackHistory(url, article);

      // 使用continuousSpeakText以保持与现有播放逻辑一致
      continuousSpeakText(article.textContent, targetTab.id);
      sendStateUpdate(targetTab.id);

      return {
        success: true,
        title: article.title || "未知标题",
        tabId: targetTab.id
      };
    } else {
      throw new Error("页面内容解析失败");
    }

  } catch (error) {
    console.error("BG: 播放URL失败:", error);
    return {
      success: false,
      error: error.message || "播放失败"
    };
  }
}

// 播放当前页面功能已集成到原有的播放逻辑中

// 等待标签页加载完成
function waitForTabLoad(tabId, timeout = 10000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    function checkTab() {
      chrome.tabs.get(tabId, (tab) => {
        if (chrome.runtime.lastError) {
          reject(new Error("标签页不存在"));
          return;
        }

        if (tab.status === 'complete') {
          resolve(tab);
        } else if (Date.now() - startTime > timeout) {
          reject(new Error("页面加载超时"));
        } else {
          setTimeout(checkTab, 500);
        }
      });
    }

    checkTab();
  });
}

// 解析标签页内容
function parseTabContent(tabId) {
  return new Promise((resolve) => {
    console.log("BG: 开始解析标签页内容:", tabId);

    // 等待解析结果的超时器
    const timeout = setTimeout(() => {
      chrome.runtime.onMessage.removeListener(messageListener);
      resolve({ success: false, error: "解析超时" });
    }, 10000);

    // 监听解析结果
    const messageListener = (message, sender) => {
      if (sender.tab?.id === tabId && message.action === 'parsedContent') {
        clearTimeout(timeout);
        chrome.runtime.onMessage.removeListener(messageListener);

        if (message.article && message.article.textContent) {
          console.log("BG: 页面内容解析成功");
          resolve({ success: true });
        } else {
          console.log("BG: 页面内容解析失败 - 无有效内容");
          resolve({ success: false, error: "页面无有效文本内容" });
        }
      }
    };

    chrome.runtime.onMessage.addListener(messageListener);

    // 尝试注入内容脚本（如果还没有注入的话）
    chrome.scripting.executeScript({
      target: { tabId: tabId },
      files: ['src/lib/Readability.js', 'src/content/parser.js']
    }).catch(error => {
      console.log("BG: 内容脚本可能已经注入，忽略错误:", error);
      // 不需要处理这个错误，因为内容脚本可能已经通过manifest注入了
    });

    // 发送消息触发解析（以防内容脚本已经加载但没有自动解析）
    chrome.tabs.sendMessage(tabId, { action: 'parseContent' }).catch(error => {
      console.log("BG: 发送解析消息失败，可能页面还在加载:", error);
    });
  });
}

// 扩展卸载或重新加载时的清理
if (isRuntimeAvailable) {
  chrome.runtime.onSuspend.addListener(() => {
    console.log("🔄 Service Worker 即将挂起");
    // 注意：Service Worker挂起不等于插件关闭
    // 只有在确认是插件卸载时才停止播放
    console.log("Service Worker挂起，但播放应该继续（除非插件被卸载）");
    // 不自动停止播放，让播放在后台继续
  });
}

// 监听扩展启动/停止
if (isRuntimeAvailable) {
  chrome.runtime.onStartup.addListener(() => {
    console.log("🚀 扩展启动");
  });

  chrome.runtime.onInstalled.addListener((details) => {
    console.log("📦 扩展安装/更新:", details.reason);

    // 如果是卸载后重新安装，清理状态
    if (details.reason === 'install') {
      console.log("首次安装，清理所有状态");
      readingState = 'idle';
      currentUtterance = null;
      currentReadingTabId = null;
    }
  });
}

// 初始化设置
loadSettings();
