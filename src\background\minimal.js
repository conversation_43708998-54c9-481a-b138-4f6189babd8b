// 最简化的Service Worker - 用于测试语法问题
console.log("🚀 最简化Service Worker启动");

// 基础变量
let readingState = 'idle';
let currentReadingTabId = null;

// 插件图标点击处理
if (typeof chrome !== 'undefined' && chrome.action) {
  chrome.action.onClicked.addListener((tab) => {
    console.log("🖱️ 插件图标被点击");
    
    if (chrome.sidePanel && tab.windowId) {
      chrome.sidePanel.open({ windowId: tab.windowId })
        .then(() => {
          console.log("✅ 侧边栏打开成功");
        })
        .catch((error) => {
          console.error("❌ 侧边栏打开失败:", error);
        });
    }
  });
}

// 消息监听
if (typeof chrome !== 'undefined' && chrome.runtime) {
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log("📨 收到消息:", message.action);
    
    switch (message.action) {
      case "getState":
        sendResponse({ 
          readingState: readingState,
          currentReadingTabId: currentReadingTabId
        });
        break;
        
      case "startReading":
        console.log("开始播放请求");
        readingState = 'reading';
        sendResponse({ status: "播放已开始" });
        break;
        
      case "stopReading":
        console.log("停止播放请求");
        readingState = 'idle';
        sendResponse({ status: "播放已停止" });
        break;
        
      default:
        sendResponse({ error: "未知操作" });
        break;
    }
  });
}

console.log("✅ 最简化Service Worker初始化完成");
