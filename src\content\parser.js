// Content Parsing Logic for 神灯 AI 朗读增强助手

console.log("神灯AI·灵阅：内容解析脚本 parser.js 已加载");

// 检查API可用性
const isRuntimeAvailable = typeof chrome !== 'undefined' && typeof chrome.runtime !== 'undefined';

try {
  // 由于 Readability.js 是先于此脚本加载的，
  // 我们可以直接使用 Readability 类
  if (typeof Readability === 'undefined') {
    throw new Error("Readability库未加载");
  }

  // 克隆 document 对象，避免 Readability 修改原始 DOM
  const documentClone = document.cloneNode(true);

  // 创建 Readability 实例
  // 可以在这里传递一些选项，例如 debug: true
  const reader = new Readability(documentClone, {
    // debug: true,
    classesToPreserve: ['article', 'content', 'main', 'text', 'story']
  });

  // 解析文章
  const article = reader.parse();

  if (article && article.textContent) {
    console.log("神灯AI·灵阅：成功解析文章", article.title);
    // 发送解析结果给背景脚本
    if (isRuntimeAvailable) {
      chrome.runtime.sendMessage(
        { action: "parsedContent", article: article },
        (response) => {
          if (chrome.runtime.lastError) {
            console.error("神灯AI·灵阅：发送解析结果失败:", chrome.runtime.lastError.message);
          } else {
            console.log("神灯AI·灵阅：解析结果已发送给背景脚本", response);
          }
        }
      );
    } else {
      console.error("神灯AI·灵阅：chrome.runtime API不可用，无法发送解析结果");
    }
  } else {
    console.warn("神灯AI·灵阅：Readability 未能成功提取文章内容");
    // 可以选择发送一个失败消息
    if (isRuntimeAvailable) {
      chrome.runtime.sendMessage(
        { action: "parseContentFailed", reason: "Readability未能提取内容" },
        (response) => {
          if (chrome.runtime.lastError) {
            console.error("神灯AI·灵阅：发送解析失败消息失败:", chrome.runtime.lastError.message);
          } else {
            console.log("神灯AI·灵阅：解析失败消息已发送", response);
          }
        }
      );
    }
  }
} catch (error) {
  console.error("神灯AI·灵阅：执行 Readability 时发生错误:", error);
  // 发送错误消息
  if (isRuntimeAvailable) {
    chrome.runtime.sendMessage(
      { action: "parseContentError", error: error.message },
      (response) => {
        if (chrome.runtime.lastError) {
          console.error("神灯AI·灵阅：发送解析错误消息失败:", chrome.runtime.lastError.message);
        } else {
          console.log("神灯AI·灵阅：解析错误消息已发送", response);
        }
      }
    );
  }
}

// 注意：内容脚本通常是一次性执行的。
// 如果需要响应来自弹出窗口或背景脚本的特定请求来重新解析，
// 则需要添加消息监听器 chrome.runtime.onMessage.addListener

// 处理来自background的消息
if (isRuntimeAvailable) {
  try {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      // 检查消息是否是请求提取内容
      if (message.action === 'extractContent') {
          // ... existing code ...
      }
      
      // 处理API不支持的通知
      if (message.action === 'showUnsupportedNotification') {
          showUnsupportedApiNotification();
          sendResponse({status: "notification_shown"});
          return true; // 保持消息通道开放以进行异步响应
      }
      
      // 保持消息通道开放（如果有异步响应）
      return true;
    });
  } catch (e) {
    console.error("神灯AI·灵阅：设置消息监听器失败:", e);
  }
}

// 显示不支持API的通知
function showUnsupportedApiNotification() {
    // 创建通知元素
    const notificationDiv = document.createElement('div');
    notificationDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        background-color: #fff;
        border: 1px solid #ccc;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        z-index: 9999;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
        max-width: 300px;
    `;
    
    notificationDiv.innerHTML = `
        <div style="margin-bottom: 10px; font-weight: bold; color: #333;">神灯AI·灵阅</div>
        <div style="margin-bottom: 15px; color: #555;">
            您的浏览器版本不支持侧边栏功能或扩展加载出现问题。请更新到最新版本的Chrome浏览器或重新安装扩展。
        </div>
        <button id="close-notification" style="
            padding: 5px 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        ">我知道了</button>
    `;
    
    // 添加到页面
    document.body.appendChild(notificationDiv);
    
    // 添加关闭按钮事件
    document.getElementById('close-notification').addEventListener('click', () => {
        notificationDiv.remove();
    });
    
    // 自动5秒后消失
    setTimeout(() => {
        if (document.body.contains(notificationDiv)) {
            notificationDiv.remove();
        }
    }, 5000);
}
