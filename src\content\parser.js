// 神灯AI·灵阅 - 内容解析脚本
console.log("🔧 神灯AI·灵阅：内容解析脚本已加载");

// 检查API可用性
const isRuntimeAvailable = typeof chrome !== 'undefined' && typeof chrome.runtime !== 'undefined';

if (!isRuntimeAvailable) {
  console.warn("⚠️ Chrome Runtime API不可用");
}

// 解析页面内容的函数
function parsePageContent() {
  console.log("📖 开始解析页面内容");

  try {
    // 检查 Readability 库是否可用
    if (typeof Readability === 'undefined') {
      console.error("❌ Readability库未加载");
      return;
    }

    // 克隆 document 对象
    const documentClone = document.cloneNode(true);

    // 创建 Readability 实例
    const reader = new Readability(documentClone, {
      classesToPreserve: ['article', 'content', 'main', 'text', 'story']
    });

    // 解析文章
    const article = reader.parse();

    if (article && article.textContent) {
      console.log("✅ 成功解析文章:", article.title);

      // 发送解析结果给背景脚本
      if (isRuntimeAvailable) {
        chrome.runtime.sendMessage({
          action: "parsedContent",
          article: article
        }).catch(error => {
          console.error("❌ 发送解析结果失败:", error);
        });
      }

      return article; // 返回解析结果
    } else {
      console.warn("⚠️ Readability 未能提取文章内容");
      return null; // 返回null表示解析失败
    }
  } catch (error) {
    console.error("❌ 解析页面时出错:", error);
    return null; // 异常时返回null
  }
}

// 注意：内容脚本通常是一次性执行的。
// 如果需要响应来自弹出窗口或背景脚本的特定请求来重新解析，
// 则需要添加消息监听器 chrome.runtime.onMessage.addListener

// 消息监听器
if (isRuntimeAvailable) {
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log("📨 Content Script收到消息:", message.action, "在页面:", window.location.href);

    if (message.action === 'parseContent') {
      console.log("🔄 开始解析页面内容");

      // 异步处理解析
      (async () => {
        try {
          const result = parsePageContent();
          if (result) {
            console.log("✅ 页面解析成功，发送结果到后台");
            sendResponse({
              status: "解析完成",
              success: true,
              url: window.location.href,
              title: document.title
            });
          } else {
            console.error("❌ 页面解析失败");
            sendResponse({
              status: "解析失败",
              success: false,
              error: "解析返回空结果"
            });
          }
        } catch (error) {
          console.error("❌ 页面解析出错:", error);
          sendResponse({
            status: "解析出错",
            success: false,
            error: error.message
          });
        }
      })();

      // 返回true表示异步响应
      return true;
    }

    // 其他消息类型
    return false;
  });

  console.log("✅ Content Script消息监听器已注册，页面:", window.location.href);
} else {
  console.error("❌ Chrome Runtime API不可用，无法注册消息监听器");
}

// 显示不支持API的通知
function showUnsupportedApiNotification() {
    // 创建通知元素
    const notificationDiv = document.createElement('div');
    notificationDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        background-color: #fff;
        border: 1px solid #ccc;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        z-index: 9999;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
        max-width: 300px;
    `;
    
    notificationDiv.innerHTML = `
        <div style="margin-bottom: 10px; font-weight: bold; color: #333;">神灯AI·灵阅</div>
        <div style="margin-bottom: 15px; color: #555;">
            您的浏览器版本不支持侧边栏功能或扩展加载出现问题。请更新到最新版本的Chrome浏览器或重新安装扩展。
        </div>
        <button id="close-notification" style="
            padding: 5px 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        ">我知道了</button>
    `;
    
    // 添加到页面
    document.body.appendChild(notificationDiv);
    
    // 添加关闭按钮事件
    document.getElementById('close-notification').addEventListener('click', () => {
        notificationDiv.remove();
    });
    
    // 自动5秒后消失
    setTimeout(() => {
        if (document.body.contains(notificationDiv)) {
            notificationDiv.remove();
        }
    }, 5000);
}
