/* 紧凑样式覆盖 - 字体层次系统 */

/* === 字体层次定义 === */
/*
层次1: 软件标题/主标题 - 16px
层次2: 区域标题/卡片标题 - 14px
层次3: 功能标签/设置项 - 12px
层次4: 普通文本/内容 - 11px
层次5: 辅助信息/提示 - 10px
层次6: 次要信息/状态 - 9px
*/

/* 基础布局修复 */
body {
    font-size: 11px !important; /* 层次4: 普通文本基准 */
    line-height: 1.3 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    height: 100vh !important;
    margin: 0 !important;
    padding: 0 !important;
}

.container {
    padding: 10px !important;
    min-height: 100vh !important;
    box-sizing: border-box !important;
}

/* 卡片样式紧凑化 */
.card {
    padding: 12px !important;
    margin-bottom: 10px !important;
    border-radius: 8px !important;
}

/* === 字体层次应用 === */

/* 层次1: 软件主标题 */
.logo-text, .app-title {
    font-size: 16px !important; /* 层次1 */
    font-weight: 600 !important;
    line-height: 1.2 !important;
}

/* 层次2: 区域标题 */
h1, h2, .section-title, .settings-header h2 {
    font-size: 14px !important; /* 层次2 */
    font-weight: 600 !important;
    margin: 8px 0 6px 0 !important;
    line-height: 1.3 !important;
}

/* 层次3: 功能标签和设置项 */
h3, .section-header h3, label, .setting-item label, .chapters-label {
    font-size: 12px !important; /* 层次3 */
    font-weight: 500 !important;
    margin: 4px 0 !important;
    line-height: 1.3 !important;
}

/* 层次4: 普通文本内容 */
p, .normal-text, button, input, select, textarea {
    font-size: 11px !important; /* 层次4 */
    line-height: 1.3 !important;
    margin: 3px 0 !important;
}

/* 层次5: 辅助信息 */
.hint-text, .duration-hint, .setting-locked-hint, .help-text {
    font-size: 10px !important; /* 层次5 */
    color: #A09CB0 !important;
    line-height: 1.2 !important;
}

/* 层次6: 次要信息 */
.time-display, .status-text, .url-text, small {
    font-size: 9px !important; /* 层次6 */
    color: #A09CB0 !important;
    line-height: 1.2 !important;
}

/* === 按钮字体层次 === */

/* 普通按钮 - 层次4 */
button {
    padding: 6px 12px !important;
    font-size: 11px !important; /* 层次4 */
    font-weight: 500 !important;
    border-radius: 6px !important;
    min-height: 28px !important;
}

/* 控制按钮 - 层次4 */
.control-btn {
    min-width: 28px !important;
    height: 28px !important;
    font-size: 11px !important; /* 层次4 */
    font-weight: 500 !important;
}

/* 主要播放按钮 - 层次3 */
.main-btn {
    min-width: 36px !important;
    height: 36px !important;
    font-size: 12px !important; /* 层次3 - 重要功能 */
    font-weight: 600 !important;
}

/* 小按钮 - 层次5 */
.small-btn, .chapter-btn {
    font-size: 10px !important; /* 层次5 */
    padding: 4px 8px !important;
    min-height: 24px !important;
}

/* 设置按钮 - 层次5 */
.settings-btn {
    font-size: 10px !important; /* 层次5 */
}

/* 控制按钮容器紧凑化 */
.control-buttons-container {
    gap: 6px !important;
    margin: 8px 0 !important;
}

/* === 状态和输入控件字体层次 === */

/* 状态指示器 - 层次3 (重要状态) */
.status-indicator {
    font-size: 12px !important; /* 层次3 */
    font-weight: 600 !important;
    padding: 6px 10px !important;
    margin: 6px 0 !important;
    border-radius: 6px !important;
}

/* 输入框 - 层次4 (用户输入) */
input, select, textarea {
    padding: 6px 8px !important;
    font-size: 11px !important; /* 层次4 */
    border-radius: 4px !important;
    font-weight: 400 !important;
}

/* 重要输入框 - 层次3 */
.smart-url-input, .chapter-input {
    font-size: 12px !important; /* 层次3 */
    font-weight: 500 !important;
    padding: 8px 10px !important;
}

/* 滑块紧凑化 */
.slider-container {
    margin: 6px 0 !important;
}

.slider-label {
    font-size: 11px !important;
    margin-bottom: 3px !important;
}

.slider {
    height: 4px !important;
}

.slider-value {
    font-size: 10px !important;
    padding: 2px 6px !important;
}

/* 章节信息紧凑化 */
.chapter-info {
    padding: 6px !important;
    margin: 4px 0 !important;
}

.chapter-title {
    font-size: 11px !important;
    margin-bottom: 2px !important;
}

.chapter-url {
    font-size: 9px !important;
}

.chapter-progress {
    font-size: 9px !important;
    margin-top: 2px !important;
}

/* 消息提示紧凑化 */
.message {
    padding: 4px 8px !important;
    font-size: 10px !important;
    margin: 3px 0 !important;
    border-radius: 3px !important;
}

/* === 语音设置字体层次 === */

.voice-settings {
    padding: 10px !important;
}

/* 语音过滤标签 - 层次5 */
.voice-filter-tabs {
    gap: 4px !important;
    margin-bottom: 8px !important;
}

.voice-filter-tab {
    padding: 4px 8px !important;
    font-size: 10px !important; /* 层次5 */
    font-weight: 500 !important;
    border-radius: 4px !important;
}

/* 性别过滤器 - 层次5 */
.gender-filters {
    gap: 8px !important;
    margin: 6px 0 !important;
}

.gender-checkbox-label {
    font-size: 10px !important; /* 层次5 */
    font-weight: 400 !important;
    gap: 4px !important;
}

/* 语音选择器 - 层次3/4 */
.voice-selector {
    margin: 8px 0 !important;
}

.voice-selector label {
    font-size: 12px !important; /* 层次3 - 功能标签 */
    font-weight: 500 !important;
    margin-bottom: 4px !important;
}

.voice-selector select {
    padding: 6px 8px !important;
    font-size: 11px !important; /* 层次4 - 选择内容 */
    min-height: 30px !important;
    font-weight: 400 !important;
}

/* 播放时长设置修复 */
.progress-section {
    margin: 12px 0 !important;
    padding: 8px !important;
    background: rgba(255, 255, 255, 0.02) !important;
    border-radius: 6px !important;
}

.progress-bar-container {
    height: 8px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 4px !important;
    position: relative !important;
    cursor: pointer !important;
    margin: 8px 0 !important;
}

.progress-bar-container #progress-bar {
    height: 100% !important;
    background: linear-gradient(90deg, #E1AD5B, #EECB86) !important;
    border-radius: 4px !important;
    transition: width 0.2s ease !important;
}

.time-bubble {
    position: absolute !important;
    top: -28px !important;
    transform: translateX(-50%) !important;
    background: #E1AD5B !important;
    color: #10121A !important;
    padding: 4px 8px !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    white-space: nowrap !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
    pointer-events: none !important;
}

.progress-bar-container:hover .time-bubble,
.progress-bar-container.dragging .time-bubble {
    opacity: 1 !important;
}

.duration-hint {
    font-size: 12px !important;
    color: #A09CB0 !important;
    text-align: center !important;
    margin: 4px 0 !important;
    font-weight: 500 !important;
}

.time-display {
    display: flex !important;
    justify-content: space-between !important;
    font-size: 11px !important;
    color: #F0F0F5 !important;
    margin-top: 6px !important;
    font-weight: 500 !important;
}

.time-display #current-time {
    color: #E1AD5B !important;
    font-weight: 600 !important;
}

.time-display #total-time {
    color: #A09CB0 !important;
}

/* 剩余播放时间显示 */
.remaining-time-display {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-top: 6px !important;
    padding: 4px 8px !important;
    background: rgba(225, 173, 91, 0.1) !important;
    border-radius: 4px !important;
    border: 1px solid rgba(225, 173, 91, 0.2) !important;
}

.remaining-label {
    font-size: 10px !important; /* 层次5 - 辅助信息 */
    color: #A09CB0 !important;
    margin-right: 6px !important;
}

.remaining-time {
    font-size: 11px !important; /* 层次4 - 重要信息 */
    color: #E1AD5B !important;
    font-weight: 600 !important;
    font-family: 'Courier New', monospace !important;
}

/* === 播放相关字体层次 === */

/* 播放计时器 - 层次3 (重要信息) */
.playback-timer {
    font-size: 12px !important; /* 层次3 */
    font-weight: 600 !important;
    padding: 6px 10px !important;
    margin: 6px 0 !important;
    background: rgba(225, 173, 91, 0.1) !important;
    border-radius: 6px !important;
    text-align: center !important;
    color: #E1AD5B !important;
}

/* 播放控制区域修复 */
.playback-controls {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    margin: 8px 0 !important;
}

/* 播放时间显示 - 层次2 (重要状态) */
.playback-time-display {
    font-size: 14px !important; /* 层次2 */
    font-weight: 600 !important;
    color: #F0F0F5 !important;
    margin: 0 8px !important;
}

.playback-progress-container {
    margin: 8px 0 !important;
}

.playback-progress-bar {
    height: 4px !important;
    border-radius: 2px !important;
}

.playback-duration-display {
    display: flex !important;
    justify-content: space-between !important;
    font-size: 10px !important;
    color: #A09CB0 !important;
    margin-top: 4px !important;
}

/* 响应式调整 */
@media (max-width: 400px) {
    .container {
        padding: 8px !important;
    }

    .card {
        padding: 10px !important;
    }

    .control-btn {
        min-width: 24px !important;
        height: 24px !important;
        font-size: 10px !important;
    }

    .main-btn {
        min-width: 32px !important;
        height: 32px !important;
        font-size: 12px !important;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 4px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 确保内容可以滚动 */
.container {
    overflow-y: visible !important;
}

/* 播放历史高度限制 - 压缩高度 */
.history-list {
    max-height: 180px !important;
    overflow-y: auto !important;
}

/* 播放内容信息字体大小调整 - 与播放历史保持一致 */
.playing-content-info {
    font-size: 10px !important;
}

.playing-content-info .playing-label {
    font-size: 10px !important;
    font-weight: normal !important;
}

.playing-content-info .playing-title {
    font-size: 11px !important; /* 层次4 - 与历史记录标题一致 */
    font-weight: 600 !important; /* 保持标题的重要性 */
}

.playing-content-info .playing-url {
    font-size: 9px !important;
    opacity: 0.8 !important;
}

.playing-content-info .open-page-btn {
    font-size: 9px !important;
    padding: 2px 4px !important;
}

.playing-content-info .open-page-text {
    font-size: 9px !important;
}

/* 播放内容区域间距调整 */
.playing-content-info {
    padding: 6px !important;
    margin: 4px 0 !important;
}

.playing-title-container {
    margin-bottom: 3px !important;
}

.playing-url-container {
    margin-top: 3px !important;
}

/* 历史记录小说信息样式 */
.history-novel-info {
    font-size: 10px !important;
    color: #4a90e2 !important;
    margin: 2px 0 !important;
    font-weight: 500 !important;
}

.history-chapter-info {
    font-size: 9px !important;
    color: #666 !important;
    margin: 1px 0 !important;
}

.history-title-text {
    font-size: 11px !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
}

.history-item {
    margin-bottom: 8px !important;
    padding: 6px !important;
    border-radius: 4px !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.history-item:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
}

.history-meta {
    font-size: 8px !important;
    margin-top: 3px !important;
}

.history-url {
    font-size: 8px !important;
    color: #888 !important;
    margin-top: 2px !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

/* 紧凑化间距 */
.section-header {
    margin-bottom: 6px !important;
}

.section-header h3 {
    font-size: 12px !important;
    margin: 0 !important;
}

/* 图标大小调整 */
.icon {
    font-size: 10px !important;
}

/* 标签和提示文本紧凑化 */
label {
    font-size: 10px !important;
    margin-bottom: 2px !important;
}

.hint-text {
    font-size: 9px !important;
    padding: 3px 6px !important;
}

/* 分隔线紧凑化 */
hr {
    margin: 6px 0 !important;
}

/* 列表项紧凑化 */
li {
    margin: 2px 0 !important;
    font-size: 10px !important;
}

ul, ol {
    margin: 4px 0 !important;
    padding-left: 16px !important;
}

/* 表格紧凑化 */
table {
    font-size: 9px !important;
}

th, td {
    padding: 2px 4px !important;
}

/* 链接紧凑化 */
a {
    font-size: 10px !important;
}

/* 代码块紧凑化 */
code, pre {
    font-size: 9px !important;
    padding: 2px 4px !important;
}

/* 引用块紧凑化 */
blockquote {
    margin: 4px 0 !important;
    padding: 4px 8px !important;
    font-size: 10px !important;
}

/* 表单元素组紧凑化 */
.form-group {
    margin: 4px 0 !important;
}

.form-group label {
    margin-bottom: 2px !important;
}

/* 工具提示紧凑化 */
.tooltip {
    font-size: 9px !important;
    padding: 2px 4px !important;
}

/* 徽章和标签紧凑化 */
.badge, .tag {
    font-size: 8px !important;
    padding: 1px 3px !important;
    border-radius: 2px !important;
}

/* 进度条紧凑化 */
.progress-bar {
    height: 2px !important;
}

/* 模态框和弹出层紧凑化 */
.modal, .popup {
    padding: 8px !important;
}

.modal-header {
    padding: 4px 8px !important;
}

.modal-body {
    padding: 6px 8px !important;
}

.modal-footer {
    padding: 4px 8px !important;
}

/* 导航和菜单紧凑化 */
.nav, .menu {
    gap: 2px !important;
}

.nav-item, .menu-item {
    padding: 3px 6px !important;
    font-size: 10px !important;
}

/* 面板和折叠区域紧凑化 */
.panel {
    margin: 4px 0 !important;
}

.panel-header {
    padding: 4px 6px !important;
    font-size: 11px !important;
}

.panel-body {
    padding: 6px !important;
}

/* 特定布局修复 */
.section-header {
    margin-bottom: 8px !important;
}

.section-header h3 {
    font-size: 12px !important; /* 层次3 - 与其他区域标题保持一致 */
    margin: 0 !important;
}

/* 确保flex布局正常工作 */
.flex-container {
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
}

.flex-between {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

/* 修复可能的文本溢出 */
.text-ellipsis {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

/* 确保按钮组布局正常 */
.button-group {
    display: flex !important;
    gap: 6px !important;
    align-items: center !important;
}

/* 修复下拉菜单样式 */
select {
    appearance: none !important;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right 8px center !important;
    background-size: 12px !important;
    padding-right: 28px !important;
}

/* === 连续播放设置字体层次 === */

/* 连续播放标签 - 层次3 */
.continuous-reading-row label,
.continuous-chapters-row .chapters-label {
    font-size: 12px !important; /* 层次3 - 功能标签 */
    font-weight: 500 !important;
}

/* 章节数输入 - 层次3 */
.chapter-input {
    font-size: 12px !important; /* 层次3 - 重要输入 */
    font-weight: 600 !important;
    text-align: center !important;
    min-height: 28px !important;
}

/* 章节控制按钮 - 层次4 */
.chapter-btn {
    font-size: 11px !important; /* 层次4 */
    font-weight: 600 !important;
    min-width: 28px !important;
    min-height: 28px !important;
}

/* 设置锁定提示 - 层次5 */
.setting-locked-hint {
    font-size: 10px !important; /* 层次5 - 辅助信息 */
    color: #A09CB0 !important;
    font-style: italic !important;
}

/* === 软件标题特别强调 === */
.logo-text {
    font-size: 16px !important; /* 层次1 - 最大 */
    font-weight: 700 !important; /* 加粗 */
    color: #F0F0F5 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3) !important; /* 添加阴影 */
}

.slogan-symbol {
    font-size: 14px !important;
    opacity: 0.8 !important;
}

/* 确保字体层次不被覆盖 */
* {
    line-height: inherit !important;
}
