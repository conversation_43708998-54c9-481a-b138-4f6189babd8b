/* 紧凑样式覆盖 */

/* 基础布局修复 */
body {
    font-size: 11px !important;
    line-height: 1.2 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    height: 100vh !important;
    margin: 0 !important;
    padding: 0 !important;
}

.container {
    padding: 6px !important;
    min-height: 100vh !important;
}

/* 卡片样式紧凑化 */
.card {
    padding: 8px !important;
    margin-bottom: 8px !important;
    border-radius: 6px !important;
}

/* 标题和文本紧凑化 */
h1, h2, h3, h4, h5, h6 {
    margin: 4px 0 !important;
    font-size: 13px !important;
    line-height: 1.2 !important;
}

p {
    margin: 3px 0 !important;
    font-size: 11px !important;
}

/* 按钮紧凑化 */
button {
    padding: 4px 8px !important;
    font-size: 10px !important;
    border-radius: 4px !important;
    min-height: 24px !important;
}

.control-btn {
    min-width: 24px !important;
    height: 24px !important;
    font-size: 10px !important;
}

.main-btn {
    min-width: 32px !important;
    height: 32px !important;
    font-size: 12px !important;
}

/* 控制按钮容器紧凑化 */
.control-buttons-container {
    gap: 4px !important;
    margin: 6px 0 !important;
}

/* 状态指示器紧凑化 */
.status-indicator {
    font-size: 10px !important;
    padding: 2px 6px !important;
    margin: 3px 0 !important;
}

/* 输入框紧凑化 */
input, select, textarea {
    padding: 4px 6px !important;
    font-size: 10px !important;
    border-radius: 3px !important;
}

/* 滑块紧凑化 */
.slider-container {
    margin: 4px 0 !important;
}

.slider-label {
    font-size: 10px !important;
    margin-bottom: 2px !important;
}

.slider {
    height: 3px !important;
}

.slider-value {
    font-size: 9px !important;
    padding: 1px 4px !important;
}

/* 章节信息紧凑化 */
.chapter-info {
    padding: 6px !important;
    margin: 4px 0 !important;
}

.chapter-title {
    font-size: 11px !important;
    margin-bottom: 2px !important;
}

.chapter-url {
    font-size: 9px !important;
}

.chapter-progress {
    font-size: 9px !important;
    margin-top: 2px !important;
}

/* 消息提示紧凑化 */
.message {
    padding: 4px 8px !important;
    font-size: 10px !important;
    margin: 3px 0 !important;
    border-radius: 3px !important;
}

/* 语音设置紧凑化 */
.voice-settings {
    padding: 6px !important;
}

.voice-filter-tabs {
    gap: 3px !important;
    margin-bottom: 6px !important;
}

.voice-filter-tab {
    padding: 3px 6px !important;
    font-size: 9px !important;
    border-radius: 3px !important;
}

.gender-filters {
    gap: 6px !important;
    margin: 4px 0 !important;
}

.gender-checkbox-label {
    font-size: 9px !important;
    gap: 3px !important;
}

/* 语音选择器紧凑化 */
.voice-selector {
    margin: 4px 0 !important;
}

.voice-selector label {
    font-size: 10px !important;
    margin-bottom: 2px !important;
}

.voice-selector select {
    padding: 3px 6px !important;
    font-size: 9px !important;
}

/* 播放时长设置紧凑化 */
.playback-duration-container {
    margin: 4px 0 !important;
}

.playback-duration-label {
    font-size: 10px !important;
    margin-bottom: 2px !important;
}

/* 播放计时器紧凑化 */
.playback-timer {
    font-size: 11px !important;
    padding: 3px 6px !important;
    margin: 3px 0 !important;
}

/* 响应式调整 */
@media (max-width: 400px) {
    .container {
        padding: 4px !important;
    }
    
    .card {
        padding: 6px !important;
    }
    
    .control-btn {
        min-width: 20px !important;
        height: 20px !important;
        font-size: 9px !important;
    }
    
    .main-btn {
        min-width: 28px !important;
        height: 28px !important;
        font-size: 11px !important;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 4px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 确保内容可以滚动 */
.container {
    overflow-y: visible !important;
}

/* 移除高度限制 */
.history-list {
    max-height: none !important;
    overflow-y: visible !important;
}

/* 紧凑化间距 */
.section-header {
    margin-bottom: 6px !important;
}

.section-header h3 {
    font-size: 12px !important;
    margin: 0 !important;
}

/* 图标大小调整 */
.icon {
    font-size: 10px !important;
}

/* 标签和提示文本紧凑化 */
label {
    font-size: 10px !important;
    margin-bottom: 2px !important;
}

.hint-text {
    font-size: 9px !important;
    padding: 3px 6px !important;
}

/* 分隔线紧凑化 */
hr {
    margin: 6px 0 !important;
}

/* 列表项紧凑化 */
li {
    margin: 2px 0 !important;
    font-size: 10px !important;
}

ul, ol {
    margin: 4px 0 !important;
    padding-left: 16px !important;
}

/* 表格紧凑化 */
table {
    font-size: 9px !important;
}

th, td {
    padding: 2px 4px !important;
}

/* 链接紧凑化 */
a {
    font-size: 10px !important;
}

/* 代码块紧凑化 */
code, pre {
    font-size: 9px !important;
    padding: 2px 4px !important;
}

/* 引用块紧凑化 */
blockquote {
    margin: 4px 0 !important;
    padding: 4px 8px !important;
    font-size: 10px !important;
}

/* 表单元素组紧凑化 */
.form-group {
    margin: 4px 0 !important;
}

.form-group label {
    margin-bottom: 2px !important;
}

/* 工具提示紧凑化 */
.tooltip {
    font-size: 9px !important;
    padding: 2px 4px !important;
}

/* 徽章和标签紧凑化 */
.badge, .tag {
    font-size: 8px !important;
    padding: 1px 3px !important;
    border-radius: 2px !important;
}

/* 进度条紧凑化 */
.progress-bar {
    height: 2px !important;
}

/* 模态框和弹出层紧凑化 */
.modal, .popup {
    padding: 8px !important;
}

.modal-header {
    padding: 4px 8px !important;
}

.modal-body {
    padding: 6px 8px !important;
}

.modal-footer {
    padding: 4px 8px !important;
}

/* 导航和菜单紧凑化 */
.nav, .menu {
    gap: 2px !important;
}

.nav-item, .menu-item {
    padding: 3px 6px !important;
    font-size: 10px !important;
}

/* 面板和折叠区域紧凑化 */
.panel {
    margin: 4px 0 !important;
}

.panel-header {
    padding: 4px 6px !important;
    font-size: 11px !important;
}

.panel-body {
    padding: 6px !important;
}

/* 确保所有文本都使用紧凑字体 */
* {
    font-size: inherit !important;
    line-height: inherit !important;
}
