<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>神灯AI·灵阅 V0.36</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="url-input-styles.css">
    <link rel="stylesheet" href="compact-styles.css">
    <!-- Link to an icon font library like Font Awesome or Material Icons (optional) -->
    <!-- <link rel="stylesheet" href="path/to/font-awesome/css/all.min.css"> -->
</head>
<body>
    <div class="container">
        <!-- Top Bar -->
        <header class="panel-header">
            <div class="logo">
                <!-- 使用软件图标 -->
                <img src="../../assets/icons/icon32.png" alt="神灯AI·灵阅" class="app-icon">
                <span class="logo-text"><span class="slogan-symbol">-=</span> 打开赛博阅读新世界 <span class="slogan-symbol">=-</span></span>
            </div>
            <button id="voice-settings-btn" class="settings-btn" title="语音设置">
                <svg class="settings-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="3"></circle>
                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                </svg>
            </button>
        </header>

        <!-- Settings Overlay -->
        <div id="settings-overlay" class="settings-overlay" style="display: none;">
            <div class="settings-content">
                <div class="settings-header">
                    <h2>语音设置</h2>
                    <button id="close-settings-btn" class="close-btn" title="关闭">×</button>
                </div>
                <div class="settings-body">
                    <div class="setting-item">
                        <label for="overlay-speed-slider">语速:</label>
                        <input type="range" id="overlay-speed-slider" class="speed-slider" min="0.5" max="10.0" value="1.1" step="0.1">
                        <span id="overlay-speed-value" class="setting-value">1.1x</span>
                    </div>
                    
                    <div class="setting-item">
                        <label>连续阅读:</label>
                        <label class="switch">
                            <input type="checkbox" id="overlay-continuous-toggle">
                            <span class="slider round"></span>
                        </label>
                    </div>
                    
                    <!-- 语音设置部分 -->
                    <div class="voice-settings-container">
                        <div class="voice-settings-header">
                            <h3>选择默认语音</h3>
                            <div class="voice-filter-tabs" style="display: none;">
                                <button class="voice-filter-tab active" data-filter="all">全部</button>
                                <button class="voice-filter-tab" data-filter="zh">中文</button>
                                <button class="voice-filter-tab" data-filter="en">英文</button>
                                <button class="voice-filter-tab" data-filter="ja">日文</button>
                                <button class="voice-filter-tab" data-filter="other">其他</button>
                            </div>
                            <div class="voice-gender-filter" style="display: none;">
                                <label class="gender-label">
                                    <input type="checkbox" class="gender-checkbox" data-gender="male" checked>
                                    <span>男声</span>
                                </label>
                                <label class="gender-label">
                                    <input type="checkbox" class="gender-checkbox" data-gender="female" checked>
                                    <span>女声</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="voice-list-container">
                            <div class="default-voice-section">
                                <label for="default-voice-select">默认语音:</label>
                                <select id="default-voice-select" class="voice-select">
                                    <option value="">默认/自动</option>
                                    <!-- 默认语音选项会通过JS填充 -->
                                </select>
                            </div>
                            
                            <div class="favorite-voices-section">
                                <h4>我的语音收藏</h4>
                                <div id="favorite-voices-list" class="favorite-voices-list">
                                    <!-- 收藏的语音会显示在这里 -->
                                    <div class="empty-favorites-message">尚未添加收藏语音</div>
                                </div>
                            </div>

                            <div class="available-voices-section" style="display: none;">
                                <h4>可用语音</h4>
                                <div class="voice-search-box">
                                    <input type="text" id="voice-search-input" placeholder="搜索语音..." class="voice-search-input">
                                </div>
                                <div id="available-voices-list" class="available-voices-list">
                                    <!-- 语音列表会通过JS填充 -->
                                    <div class="loading-voices">加载中...</div>
                                </div>
                            </div>
                            
                            <!-- 添加管理语音收藏的按钮 -->
                            <div class="manage-voices-section">
                                <button id="manage-voices-btn" class="manage-voices-btn">管理语音收藏</button>
                            </div>
                        </div>
                        
                        <!-- AI TTS设置容器 -->
                        <div class="ai-tts-section">
                            <h3>AI语音设置 <span class="badge coming-soon">即将推出</span></h3>
                            <div class="ai-tts-placeholder">
                                <p>神灯AI语音功能即将推出，敬请期待！</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="settings-footer">
                        <button id="save-voices-settings" class="save-button">保存设置</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- URL输入区域 -->
        <section class="card url-input-card">
            <div class="url-input-section">
                <div class="section-header">
                    <h3>播放内容</h3>
                </div>

                <!-- URL输入框 -->
                <div class="url-input-container">
                    <input type="url" id="smart-url-input" placeholder="输入网址播放指定内容，留空播放当前页面" class="smart-url-input" title="输入网址或留空播放当前页面">
                    <div id="url-validation-message" class="url-validation-message" style="display: none;"></div>
                </div>

                <!-- 播放历史记录 -->
                <div class="playback-history-section">
                    <div class="history-header" id="history-header">
                        <span class="history-title">播放历史</span>
                        <button class="history-toggle-btn" id="history-toggle-btn" title="展开/折叠历史记录">
                            <span class="toggle-icon">▼</span>
                        </button>
                    </div>
                    <div class="history-content" id="history-content" style="display: none;">
                        <div class="history-list" id="history-list">
                            <div class="empty-history">暂无播放历史</div>
                        </div>
                        <div class="history-actions">
                            <button class="clear-history-btn" id="clear-history-btn" title="清空所有历史记录">
                                <span class="clear-icon">🗑️</span>
                                <span class="clear-text">清空历史</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Core Controls -->
        <section class="card controls-card">
            <div class="playback-controls">
                <!-- 播放时长计时器 -->
                <div id="playback-timer" class="playback-timer">00:00:00</div>

                <!-- 控制按钮容器 -->
                <div class="control-buttons-container">
                    <!-- Placeholder for Previous Sentence - Add if needed -->
                    <!-- <button id="prev-sentence-btn" class="control-btn small-btn" title="上一句">⏮</button> -->
                    <button id="play-pause-btn" class="control-btn main-btn" title="点击直接播放当前页">
                        <span class="icon-play"></span>
                        <span class="icon-pause"></span>
                    </button>
                    <button id="stop-btn" class="control-btn small-btn" title="停止"></button>
                    <!-- Placeholder for Next Sentence - Add if needed -->
                    <!-- <button id="next-sentence-btn" class="control-btn small-btn" title="下一句">⏭</button> -->
                </div>

                <!-- 状态指示器 -->
                <div id="status-indicator" class="status-indicator idle">空闲</div>
            </div>

            <!-- 播放状态信息 -->
            <div class="playback-status-info">
                <div class="playing-content-info" id="playing-content-info" style="display: none;">
                    <div class="playing-title-container">
                        <span class="playing-label">正在播放：</span>
                        <span id="playing-title" class="playing-title">未播放</span>
                    </div>
                    <div class="playing-url-container">
                        <span id="playing-url" class="playing-url"></span>
                        <button id="open-playing-page-btn" class="open-page-btn" title="在当前页面打开正在播放的内容">
                            <span class="open-page-icon">🔗</span>
                            <span class="open-page-text">打开页面</span>
                        </button>
                    </div>
                </div>
            </div>
            <!-- Countdown Timer Setting / Display -->
            <!-- NOTE: This section now functions as a countdown timer setter & display -->
            <!-- User clicks/drags to set duration. JS handles logic. -->
            <div class="progress-section">
                <div class="progress-bar-container" title="点击或拖动设置朗读时长 (最长12小时)">
                    <div id="progress-bar" style="width: 1.39%;"></div> <!-- Initial width set to 10分钟/12小时 = 1.39% -->
                    <div class="time-bubble" id="time-bubble">10分钟</div> <!-- 添加时间气泡 -->
                </div>
                <div class="duration-hint">设置播放时长</div>
                <div class="time-display">
                    <span id="current-time">00:30:00</span> <!-- 默认显示30分钟 -->
                    <span id="total-time">12:00:00</span>   <!-- Shows set total duration (max 12h) -->
                </div>
                <div class="remaining-time-display" id="remaining-time-display" style="display: none;">
                    <span class="remaining-label">剩余播放时间：</span>
                    <span id="remaining-time" class="remaining-time">--:--:--</span>
                </div>
            </div>
        </section>

        <!-- Settings -->
        <section class="card settings-card">
            <!-- 水平控制面板 -->
            <div class="horizontal-controls-panel">
                <!-- 语速控制 -->
                <div class="control-container speed-container">
                    <div class="control-interaction">
                        <button id="speed-decrease-btn" class="panel-control-btn decrease-btn" title="降低语速">-</button>
                        <span id="speed-display" class="control-value">1.0x</span>
                        <button id="speed-increase-btn" class="panel-control-btn increase-btn" title="提高语速">+</button>
                    </div>
                    <div class="control-label">语速</div>
                </div>

                <!-- 连续播放开关 -->
                <div class="control-container toggle-container">
                    <div class="control-interaction">
                        <label class="switch">
                            <input type="checkbox" id="continuous-reading-toggle">
                            <span class="slider round"></span>
                        </label>
                    </div>
                    <div class="control-label">连续播放</div>
                </div>

                <!-- 播放章节数控制 -->
                <div class="control-container chapters-container">
                    <div class="control-interaction">
                        <button id="decrement-chapter-btn" class="panel-control-btn decrease-btn" title="减少章节数">-</button>
                        <span id="chapters-display" class="control-value">1</span>
                        <button id="increment-chapter-btn" class="panel-control-btn increase-btn" title="增加章节数">+</button>
                    </div>
                    <div class="control-label">播放章节</div>
                </div>
            </div>

            <!-- 语音选择区域 -->
            <div class="voice-control-section">
                <div class="voice-header-row">
                    <label for="voice-select">常用语音：</label>
                    <a href="#" id="quick-manage-voices" class="voice-settings-link">语音设置</a>
                </div>
                <div class="voice-select-container">
                    <select id="voice-select" class="voice-select">
                        <option value="">加载中...</option>
                        <!-- 选项通过JS动态生成 -->
                    </select>
                </div>
            </div>

            <!-- 隐藏的原始控件（保持兼容性） -->
            <div style="display: none;">
                <input type="range" id="speed-slider" class="speed-slider" min="0.5" max="10.0" value="1.1" step="0.1">
                <span id="speed-value" class="setting-value">1.1x</span>
                <input type="number" id="continuous-read-chapters" name="continuous-read-chapters" min="1" value="1" class="chapter-input">
            </div>

            <div class="setting-locked-hint" id="setting-locked-hint" style="display: none;">播放中无法修改设置</div>
        </section>
        
        <!-- (Future) AI Features Placeholder -->
        <!-- 
        <section class="card ai-features-card collapsed">
            <button id="toggle-ai-btn">AI 功能 ✨</button>
            <div id="ai-content" style="display: none;">
                <p>摘要、问答等...</p> 
            </div>
        </section>
        -->
        
        <!-- 添加可折叠的智能章节卡片到底部 -->
        <section class="card chapter-info-card collapsible">
            <div class="card-header-collapsible">
                <h3>当前章节：<span id="current-chapter-title-brief">内容加载中...</span></h3>
                <button class="toggle-chapter-card" title="展开/折叠章节信息">▼</button>
            </div>
            <div class="chapter-details" style="display: none;">
                <p><strong>完整标题：</strong> <span id="current-chapter-title">内容加载中...</span></p>
                <p><strong>播放起始：</strong> <span id="start-sentence">内容加载中...</span></p>
                <p><strong>播放终止：</strong> <span id="end-sentence">内容加载中...</span></p>
                <p><strong>待播放章节：</strong> <span id="next-chapter">暂无预加载内容</span></p>
            </div>
        </section>

        <!-- Status/Message Area -->
        <footer class="panel-footer">
            <p id="message-area" class="message-area"></p>
        </footer>

    </div>
    
    <!-- 语音设置内嵌浮层 -->
    <div id="voice-settings-overlay" class="voice-settings-overlay">
        <div class="voice-settings-content">
            <div class="voice-settings-inner">
                <!-- 语音设置内容将通过JS动态加载 -->
                <div id="voice-settings-container"></div>
            </div>
        </div>
    </div>

    <script src="index.js"></script>
</body>
</html>
