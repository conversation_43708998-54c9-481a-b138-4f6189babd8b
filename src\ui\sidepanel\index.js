// Side Panel UI Logic for 神灯 AI 朗读增强助手

console.log("Side Panel script loaded.");

// --- Default State ---
const initialState = {
    readingState: 'idle',
    currentSpeed: 1.1, 
    currentVoice: null, 
    continuousReading: false,
    article: null, 
    error: null,
    continuousChapters: 30,
    readingDuration: 30 * 60 // 默认30分钟 (1800秒)
};
let currentState = { ...initialState }; // Initialize with default state

// 添加状态同步标记和计时器
let lastStateUpdateTime = 0;
let stateCheckInterval = null;

// 标记是否等待自动播放（当刷新页面后）
let pendingAutoPlay = false;

// --- DOM Elements ---
const statusIndicatorEl = document.getElementById('status-indicator');
const playPauseButton = document.getElementById('play-pause-btn');
const playIcon = playPauseButton.querySelector('.icon-play');
const pauseIcon = playPauseButton.querySelector('.icon-pause');
const stopBtn = document.getElementById('stop-btn');
const speedSlider = document.getElementById('speed-slider');
const speedValueEl = document.getElementById('speed-value');
const voiceSelect = document.getElementById('voice-select');
const continuousReadingToggle = document.getElementById('continuous-reading-toggle');
const decrementChapterBtn = document.getElementById('decrement-chapter-btn');
const incrementChapterBtn = document.getElementById('increment-chapter-btn');
const chapterInput = document.getElementById('continuous-read-chapters');

// Settings overlay elements
const settingsBtn = document.getElementById('voice-settings-btn');
const settingsOverlay = document.getElementById('settings-overlay');
const closeSettingsBtn = document.getElementById('close-settings-btn');
const overlaySpeedSlider = document.getElementById('overlay-speed-slider');
const overlaySpeedValue = document.getElementById('overlay-speed-value');
const overlayContinuousToggle = document.getElementById('overlay-continuous-toggle');

// 新增的语音设置UI元素
const voiceFilterTabs = document.querySelectorAll('.voice-filter-tab');
const genderCheckboxes = document.querySelectorAll('.gender-checkbox');
const defaultVoiceSelect = document.getElementById('default-voice-select');

// 智能URL输入和播放历史相关元素
const smartUrlInput = document.getElementById('smart-url-input');
const urlValidationMessage = document.getElementById('url-validation-message');
const historyHeader = document.getElementById('history-header');
const historyToggleBtn = document.getElementById('history-toggle-btn');
const historyContent = document.getElementById('history-content');
const historyList = document.getElementById('history-list');
const clearHistoryBtn = document.getElementById('clear-history-btn');
const playingContentInfo = document.getElementById('playing-content-info');
const playingTitle = document.getElementById('playing-title');
const playingUrl = document.getElementById('playing-url');
const openPlayingPageBtn = document.getElementById('open-playing-page-btn');
const favoriteVoicesList = document.getElementById('favorite-voices-list');
const availableVoicesList = document.getElementById('available-voices-list');
const voiceSearchInput = document.getElementById('voice-search-input');
const saveVoicesSettingsBtn = document.getElementById('save-voices-settings');

const progressBarContainer = document.querySelector('.progress-bar-container');
const progressBar = document.getElementById('progress-bar');
const currentTimeEl = document.getElementById('current-time');
const totalTimeEl = document.getElementById('total-time');
const timeBubbleEl = document.getElementById('time-bubble');
const remainingTimeDisplayEl = document.getElementById('remaining-time-display');
const remainingTimeEl = document.getElementById('remaining-time');

const messageAreaEl = document.getElementById('message-area'); 
const footerEl = document.querySelector('.panel-footer'); 

const startSentenceEl = document.getElementById('start-sentence');
const endSentenceEl = document.getElementById('end-sentence');
const playbackTimerEl = document.getElementById('playback-timer');
const currentChapterTitleEl = document.getElementById('current-chapter-title');

let availableVoices = [];
let favoriteVoices = []; // 用户收藏的语音列表

// 获取章节卡片相关元素
const chapterCardHeader = document.querySelector('.card-header-collapsible');
const toggleChapterCardBtn = document.querySelector('.toggle-chapter-card');
const chapterDetails = document.querySelector('.chapter-details');
const currentChapterTitleBrief = document.getElementById('current-chapter-title-brief');

// --- 语音相关函数 ---
// 从localStorage加载收藏的语音
function loadFavoriteVoices() {
    try {
        // 优先从chrome.storage.local加载，这是扩展存储的标准位置
        chrome.storage.local.get('favoriteVoices', (result) => {
            if (result.favoriteVoices) {
                favoriteVoices = result.favoriteVoices;
                console.log("从chrome.storage.local加载收藏的语音:", favoriteVoices);
                renderFavoriteVoices(); // 重新渲染
            } else {
                // 回退到localStorage
                const savedFavorites = localStorage.getItem('favoriteVoices');
                if (savedFavorites) {
                    favoriteVoices = JSON.parse(savedFavorites);
                    console.log("从localStorage加载收藏的语音:", favoriteVoices);
                    
                    // 同步到chrome.storage.local
                    chrome.storage.local.set({ favoriteVoices });
                    
                    renderFavoriteVoices(); // 重新渲染
                }
            }
        });
    } catch (error) {
        console.error("加载收藏的语音失败:", error);
        favoriteVoices = [];
    }
}

// 保存收藏的语音到存储
function saveFavoriteVoices() {
    try {
        // 保存到localStorage
        localStorage.setItem('favoriteVoices', JSON.stringify(favoriteVoices));
        
        // 同时保存到chrome.storage.local
        chrome.storage.local.set({ favoriteVoices }, () => {
            if (chrome.runtime.lastError) {
                console.error("保存收藏语音到chrome.storage失败:", chrome.runtime.lastError);
            } else {
                console.log("收藏语音已保存到storage:", favoriteVoices);
            }
        });
    } catch (error) {
        console.error("保存收藏语音失败:", error);
    }
}

// 添加语音到收藏夹
function addVoiceToFavorites(voiceName) {
    if (!favoriteVoices.includes(voiceName)) {
        favoriteVoices.push(voiceName);
        saveFavoriteVoices();
        renderFavoriteVoices();
        renderAvailableVoices(); // 更新可用语音列表中的收藏状态
    }
}

// 从收藏夹移除语音
function removeVoiceFromFavorites(voiceName) {
    const index = favoriteVoices.indexOf(voiceName);
    if (index !== -1) {
        favoriteVoices.splice(index, 1);
        saveFavoriteVoices();
        renderFavoriteVoices();
        renderAvailableVoices(); // 更新可用语音列表中的收藏状态
    }
}

// 检测语音所属语言类别
function detectLanguageCategory(langCode) {
    if (!langCode) return 'other';
    
    langCode = langCode.toLowerCase().trim();
    
    if (langCode.startsWith('zh') || langCode.startsWith('cmn')) {
        return 'zh';
    } else if (langCode.startsWith('en')) {
        return 'en';
    } else if (langCode.startsWith('ja')) {
        return 'ja';
    } else {
        return 'other';
    }
}

// 渲染收藏的语音列表
function renderFavoriteVoices() {
    favoriteVoicesList.innerHTML = '';
    
    // 确保favoriteVoices是有效的数组
    if (!favoriteVoices || !Array.isArray(favoriteVoices) || favoriteVoices.length === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'empty-favorites-message';
        emptyMessage.textContent = '尚未添加语音收藏。点击"管理语音收藏"来添加。';
        favoriteVoicesList.appendChild(emptyMessage);
        return;
    }
    
    // 按语言分组显示收藏的语音
    const voicesByLang = {};
    
    // 首先按语言分组
    favoriteVoices.forEach(voiceName => {
        // 确保voiceName有效
        if (!voiceName) return;
        
        const voice = availableVoices.find(v => v && v.voiceName === voiceName);
        if (!voice) return; // 跳过找不到的语音
        
        const langCode = voice.lang ? voice.lang.split('-')[0] : 'unknown';
        if (!voicesByLang[langCode]) {
            voicesByLang[langCode] = [];
        }
        voicesByLang[langCode].push(voice);
    });
    
    // 如果有超过一种语言，添加语言分组标签
    const hasMultipleLanguages = Object.keys(voicesByLang).length > 1;
    
    // 遍历每种语言创建语音芯片
    for (const langCode in voicesByLang) {
        if (hasMultipleLanguages) {
            // 添加语言分组标签
            const langLabel = document.createElement('div');
            langLabel.className = 'language-group-label';
            langLabel.textContent = getLangName(langCode);
            favoriteVoicesList.appendChild(langLabel);
        }
        
        // 为该语言的每个语音创建芯片
        voicesByLang[langCode].forEach(voice => {
            const chip = document.createElement('div');
            chip.className = 'voice-chip favorite';
            chip.dataset.voiceName = voice.voiceName;
            
            // 如果是当前选中的语音，添加选中样式
            if (voice.voiceName === currentState.currentVoice) {
                chip.classList.add('selected');
            }
            
            const nameSpan = document.createElement('span');
            nameSpan.className = 'voice-chip-name';
            nameSpan.title = voice.voiceName;
            // 移除"Microsoft"前缀
            let displayName = voice.voiceName.replace(/^Microsoft\s+/i, '');
            nameSpan.textContent = displayName;
            
            const langSpan = document.createElement('span');
            langSpan.className = 'voice-chip-lang';
            langSpan.textContent = voice.lang || '?';
            
            const removeBtn = document.createElement('button');
            removeBtn.className = 'voice-chip-remove';
            removeBtn.innerHTML = '×';
            removeBtn.title = '移除收藏';
            removeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                removeVoiceFromFavorites(voice.voiceName);
            });
            
            chip.appendChild(nameSpan);
            chip.appendChild(langSpan);
            chip.appendChild(removeBtn);
            
            // 点击整个芯片选择为默认语音
            chip.addEventListener('click', () => {
                // 更新两个下拉框的值
                voiceSelect.value = voice.voiceName;
                defaultVoiceSelect.value = voice.voiceName;
                
                // 发送选择语音的消息到背景脚本
                try {
                    chrome.runtime.sendMessage({ action: 'setVoice', payload: voice.voiceName }, (response) => {
                        if (chrome.runtime.lastError) {
                            console.error("设置语音时出错:", chrome.runtime.lastError);
                            showMessage(`设置语音时出错: ${chrome.runtime.lastError.message}`, true);
                        } else {
                            console.log("设置语音响应:", response);
                            
                            // 更新当前状态
                            currentState.currentVoice = voice.voiceName;
                            
                            // 重新渲染以更新选中状态
                            renderFavoriteVoices();
                        }
                    });
                } catch (error) {
                    console.error("发送设置语音消息时出错:", error);
                    showMessage(`设置语音时出错: ${error.message}`, true);
                }
            });
            
            favoriteVoicesList.appendChild(chip);
        });
    }
}

// 渲染可用语音列表（带过滤和搜索功能）
function renderAvailableVoices() {
    console.log("渲染可用语音列表, 可用语音总数:", availableVoices?.length || 0);
    
    try {
        availableVoicesList.innerHTML = '';
        
        // 确保可用语音列表有效
        if (!availableVoices || !Array.isArray(availableVoices) || availableVoices.length === 0) {
            const noVoicesMsg = document.createElement('div');
            noVoicesMsg.className = 'loading-voices';
            noVoicesMsg.textContent = '无可用语音';
            availableVoicesList.appendChild(noVoicesMsg);
            return;
        }
        
        // 获取当前过滤条件
        const activeFilter = document.querySelector('.voice-filter-tab.active')?.dataset.filter || 'all';
        console.log("当前语言过滤:", activeFilter);
        
        // 确保性别复选框存在并默认选中
        const maleCheckbox = document.querySelector('.gender-checkbox[data-gender="male"]');
        const femaleCheckbox = document.querySelector('.gender-checkbox[data-gender="female"]');
        
        if (!maleCheckbox || !femaleCheckbox) {
            console.warn("未找到性别复选框");
        } else {
            // 确保默认选中
            if (!maleCheckbox.checked && !femaleCheckbox.checked) {
                console.log("没有选中的性别过滤器，默认全选");
                maleCheckbox.checked = true;
                femaleCheckbox.checked = true;
            }
        }
        
        const maleChecked = maleCheckbox?.checked !== false;
        const femaleChecked = femaleCheckbox?.checked !== false;
        console.log("性别过滤 - 男声:", maleChecked, "女声:", femaleChecked);
        
        const searchText = voiceSearchInput?.value?.toLowerCase() || '';
        console.log("搜索文本:", searchText);
        
        // 过滤语音
        const filteredVoices = availableVoices.filter(voice => {
            // 语言过滤
            if (activeFilter !== 'all') {
                const category = detectLanguageCategory(voice.lang);
                if (category !== activeFilter) return false;
            }
            
            // 性别过滤
            if (voice.gender) {
                if (voice.gender.toLowerCase() === 'male' && !maleChecked) return false;
                if (voice.gender.toLowerCase() === 'female' && !femaleChecked) return false;
            }
            
            // 搜索过滤
            if (searchText) {
                const nameMatch = voice.voiceName.toLowerCase().includes(searchText);
                const langMatch = voice.lang && voice.lang.toLowerCase().includes(searchText);
                if (!nameMatch && !langMatch) return false;
            }
            
            return true;
        });
        
        console.log("过滤后的语音数量:", filteredVoices.length);
        
        if (filteredVoices.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'loading-voices';
            noResults.textContent = '没有匹配的语音...';
            availableVoicesList.appendChild(noResults);
            return;
        }
        
        // 当前选中的语音
        const selectedVoice = defaultVoiceSelect.value;
        console.log("当前选中的语音:", selectedVoice);
        
        // 创建语音项
        let renderedCount = 0;
        filteredVoices.forEach(voice => {
            const item = document.createElement('div');
            item.className = 'voice-item';
            item.dataset.voiceName = voice.voiceName;
            
            // 是否是收藏的语音
            if (favoriteVoices.includes(voice.voiceName)) {
                item.classList.add('favorite');
            }
            
            // 是否是当前选中的语音
            if (voice.voiceName === selectedVoice) {
                item.classList.add('selected');
            }
            
            const nameDiv = document.createElement('div');
            nameDiv.className = 'voice-item-name';
            nameDiv.title = voice.voiceName;
            // 移除"Microsoft"前缀
            let displayName = voice.voiceName.replace(/^Microsoft\s+/i, '');
            nameDiv.textContent = displayName;
            
            const favoriteIcon = document.createElement('span');
            favoriteIcon.className = 'voice-item-favorite';
            favoriteIcon.innerHTML = '★';
            favoriteIcon.title = favoriteVoices.includes(voice.voiceName) ? '取消收藏' : '添加到收藏';
            
            const detailsDiv = document.createElement('div');
            detailsDiv.className = 'voice-item-details';
            
            const langSpan = document.createElement('span');
            langSpan.className = 'voice-item-lang';
            langSpan.textContent = voice.lang || '未知';
            
            const genderSpan = document.createElement('span');
            genderSpan.className = 'voice-item-gender';
            genderSpan.textContent = voice.gender || '未知';
            
            detailsDiv.appendChild(langSpan);
            detailsDiv.appendChild(genderSpan);
            
            item.appendChild(nameDiv);
            item.appendChild(favoriteIcon);
            item.appendChild(detailsDiv);
            
            // 点击语音项选择默认语音
            item.addEventListener('click', () => {
                defaultVoiceSelect.value = voice.voiceName;
                // 重新渲染以更新选中状态
                renderAvailableVoices();
            });
            
            // 点击收藏图标
            favoriteIcon.addEventListener('click', (e) => {
                e.stopPropagation(); // 防止触发父元素点击
                
                if (favoriteVoices.includes(voice.voiceName)) {
                    removeVoiceFromFavorites(voice.voiceName);
                } else {
                    addVoiceToFavorites(voice.voiceName);
                }
            });
            
            availableVoicesList.appendChild(item);
            renderedCount++;
        });
        
        console.log("成功渲染的语音项数量:", renderedCount);
    } catch (error) {
        console.error("渲染可用语音列表时出错:", error);
        // 显示错误信息
        const errorMsg = document.createElement('div');
        errorMsg.className = 'loading-voices';
        errorMsg.textContent = '渲染语音列表出错: ' + error.message;
        availableVoicesList.appendChild(errorMsg);
    }
}

// --- 原有事件监听器 ---
playPauseButton.addEventListener('click', () => {
    console.log("播放/暂停按钮被点击，当前状态:", currentState.readingState);
    
    // 确保读取最新状态，避免状态不同步导致问题
    if (Date.now() - lastStateUpdateTime > 3000) {
        console.log("播放/暂停按钮: 状态可能过期，先请求更新");
        requestStateUpdate();
        
        // 延迟处理点击，等待状态更新
        setTimeout(() => {
            handlePlayPauseClick();
        }, 300);
        return;
    }
    
    // 立即处理点击
    handlePlayPauseClick();
});

// 播放/暂停按钮点击处理逻辑
function handlePlayPauseClick() {
    // 处理导航状态 - 当处于导航状态时，暂时不响应点击
    if (currentState.readingState === 'navigating') {
        console.log("正在章节切换中，暂不响应播放/暂停点击");
        showMessage("正在切换到下一章节，请稍候...", false);
        return;
    }
    
    if (currentState.readingState === 'reading') {
        console.log("暂停按钮被点击");
        try {
            chrome.runtime.sendMessage({ action: 'pauseReading' }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error("暂停朗读时出错:", chrome.runtime.lastError);
                    showMessage(`暂停朗读时出错: ${chrome.runtime.lastError.message}`, true);
                } else {
                    console.log("暂停朗读响应:", response);
                    pausePlaybackTimer();
                    
                    // 立即更新按钮状态，不等待下一次状态更新
                    playPauseButton.classList.remove('playing');
                    playPauseButton.title = "继续播放当前页";
                    statusIndicatorEl.textContent = translateState('paused');
                    statusIndicatorEl.className = 'status-indicator paused';
                }
            });
        } catch (error) {
            console.error("发送暂停朗读消息时出错:", error);
            showMessage(`暂停朗读时出错: ${error.message}`, true);
        }
    } else {
        console.log("播放按钮被点击, 当前状态:", currentState.readingState);

        // 智能播放逻辑：检查URL输入框
        const inputUrl = smartUrlInput ? smartUrlInput.value.trim() : '';

        if (inputUrl) {
            // 有URL输入，播放指定URL
            console.log("检测到URL输入，播放指定网址:", inputUrl);
            handleSmartPlayURL(inputUrl);
            return;
        }

        // 没有URL输入，播放当前页面（原有逻辑）
        console.log("没有URL输入，播放当前页面");

        // 检查文章是否正在加载中
        if (currentState.readingState === 'loading') {
            console.log("文章正在加载中，禁止开始播放");
            showMessage("文章正在加载中，请稍候...", false);
            return;
        }
        
        // 检查文章是否存在
        if (!currentState.article || !currentState.article.textContent) {
            console.log("文章尚未加载，正在解析内容...");
            showMessage("正在解析页面内容，请稍候...", false);

            // 更新状态为加载中
            statusIndicatorEl.textContent = '正在解析';
            statusIndicatorEl.className = 'status-indicator loading';

            // 直接调用startReading，让后台脚本处理解析逻辑
            chrome.runtime.sendMessage({ action: 'startReading' }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error("开始播放时出错:", chrome.runtime.lastError);
                    showMessage(`开始播放时出错: ${chrome.runtime.lastError.message}`, true);
                    statusIndicatorEl.textContent = '空闲';
                    statusIndicatorEl.className = 'status-indicator idle';
                } else if (response && response.status) {
                    console.log("播放请求已发送:", response.status);
                    // 状态更新会通过后台脚本的状态更新机制处理
                } else {
                    console.error("播放请求失败:", response);
                    showMessage("播放失败，请稍后重试", true);
                    statusIndicatorEl.textContent = '空闲';
                    statusIndicatorEl.className = 'status-indicator idle';
                }
            });
            return;
        }
        
        // 如果是从暂停状态恢复，则调用resumeReading，否则调用startReading
        if (currentState.readingState === 'paused') {
            startReadingFromPaused();
        } else {
            startReadingFromBeginning();
            // 历史记录由后台脚本在解析完成时自动添加
        }
    }
}

// 从暂停状态恢复播放
function startReadingFromPaused() {
    console.log("从暂停位置恢复朗读");
    try {
        chrome.runtime.sendMessage({ action: 'resumeReading' }, (response) => {
            if (chrome.runtime.lastError) {
                console.error("恢复朗读时出错:", chrome.runtime.lastError);
                showMessage(`恢复朗读时出错: ${chrome.runtime.lastError.message}`, true);
            } else {
                console.log("恢复朗读响应:", response);
                startPlaybackTimer();
                
                // 立即更新按钮状态，不等待下一次状态更新
                playPauseButton.classList.add('playing');
                playPauseButton.title = "暂停播放";
                statusIndicatorEl.textContent = translateState('reading');
                statusIndicatorEl.className = 'status-indicator reading';

                // 强制更新当前状态，确保UI同步
                currentState.readingState = 'reading';
                console.log("✅ 恢复播放：按钮状态已更新为暂停图标");

                // 延迟请求状态更新，确保与后台同步
                setTimeout(() => {
                    requestStateUpdate();
                }, 500);
            }
        });
    } catch (error) {
        console.error("发送恢复朗读消息时出错:", error);
        showMessage(`恢复朗读时出错: ${error.message}`, true);
    }
}

// 从头开始播放
function startReadingFromBeginning() {
    console.log("从头开始朗读");
    try {
        chrome.runtime.sendMessage({ action: 'startReading' }, (response) => {
            if (chrome.runtime.lastError) {
                console.error("开始朗读时出错:", chrome.runtime.lastError);
                showMessage(`开始朗读时出错: ${chrome.runtime.lastError.message}`, true);
                
                // 如果出错，可能是因为内容未能成功解析，尝试刷新页面
                if (chrome.runtime.lastError.message.includes("Content not available")) {
                    showMessage("内容不可用，将尝试刷新页面...", false);
                    reloadCurrentTabAndRetry();
                }
            } else {
                console.log("开始朗读响应:", response);
                resetPlaybackTimer();
                startPlaybackTimer();
                
                // 立即更新按钮状态，不等待下一次状态更新
                playPauseButton.classList.add('playing');
                playPauseButton.title = "暂停播放";
                statusIndicatorEl.textContent = translateState('reading');
                statusIndicatorEl.className = 'status-indicator reading';

                // 强制更新当前状态，确保UI同步
                currentState.readingState = 'reading';
                console.log("✅ 播放开始：按钮状态已更新为暂停图标");

                // 延迟请求状态更新，确保与后台同步
                setTimeout(() => {
                    requestStateUpdate();
                }, 500);
            }
        });
    } catch (error) {
        console.error("发送开始朗读消息时出错:", error);
        showMessage(`开始朗读时出错: ${error.message}`, true);
    }
}

// 函数已移除，现在直接在主播放逻辑中处理

// 刷新当前标签页并重试操作
function reloadCurrentTabAndRetry() {
    // 更新UI状态
    statusIndicatorEl.textContent = '正在刷新';
    statusIndicatorEl.className = 'status-indicator loading';
    
    // 设置标记，表示刷新后应该自动播放
    pendingAutoPlay = true;
    console.log("设置页面刷新后自动播放标记");
    
    // 显示刷新中的消息
    showMessage("正在刷新页面，解析完成后将自动播放...", false);
    
    // 延迟2秒后刷新页面，让用户有时间看到提示信息
    setTimeout(() => {
        // 向背景脚本请求当前阅读标签页ID
        chrome.runtime.sendMessage({ action: 'getState' }, (response) => {
            if (chrome.runtime.lastError) {
                console.error("获取阅读标签页ID时出错:", chrome.runtime.lastError);
                fallbackToActiveTab();
                return;
            }
            
            // 检查响应中是否包含有效状态
            if (response && response.state && response.state.article) {
                const readingTabId = response.state.article.tabId || null;
                
                if (readingTabId) {
                    console.log("使用当前阅读标签页进行刷新:", readingTabId);
                    refreshTab(readingTabId);
                } else {
                    console.log("响应中没有找到有效的阅读标签页ID，回退到活动标签页");
                    fallbackToActiveTab();
                }
            } else {
                console.log("未在响应中找到阅读标签页信息，回退到活动标签页");
                fallbackToActiveTab();
            }
        });
        
        // 回退函数：使用当前活动标签页
        function fallbackToActiveTab() {
            console.log("回退到刷新当前活动标签页");
            chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
                if (tabs && tabs.length > 0) {
                    const currentTab = tabs[0];
                    console.log("使用当前活动标签页进行刷新:", currentTab.id);
                    refreshTab(currentTab.id);
                } else {
                    console.error("无法获取当前活动标签页");
                    showMessage("无法自动刷新，请手动刷新页面", true);
                    pendingAutoPlay = false; // 重置标记
                }
            });
        }
        
        // 刷新指定标签页
        function refreshTab(tabId) {
            // 通知背景脚本这是播放按钮发起的自动刷新
            chrome.runtime.sendMessage({
                action: 'markAutoRefreshForPlay',
                tabId: tabId
            }, () => {
                // 刷新页面
                chrome.tabs.reload(tabId, {}, () => {
                    console.log("页面刷新已触发，等待页面加载和内容解析...");
                });
            });
        }
    }, 2000);
}

// 重置计时器，添加额外的安全检查和日志
function resetPlaybackTimer() {
    console.log("resetPlaybackTimer被调用，当前playbackTimer状态:", playbackTimer);
    try {
        if (playbackTimer) {
            console.log("清除现有计时器");
            clearInterval(playbackTimer);
            playbackTimer = null;
        } else {
            console.log("没有活动的计时器需要清除");
        }
        playbackElapsedTime = 0;
        playbackTimerEl.textContent = "00:00:00";
        console.log("计时器重置完成");
    } catch (error) {
        console.error("重置计时器时出错:", error);
        // 强制重置所有相关变量
        playbackTimer = null;
        playbackElapsedTime = 0;
        playbackTimerEl.textContent = "00:00:00";
    }
}

// 增强停止按钮的点击事件处理
stopBtn.addEventListener('click', () => { 
    console.log("Stop button clicked, 当前状态:", currentState.readingState);
    
    // 确保读取最新状态，避免状态不同步导致问题
    if (Date.now() - lastStateUpdateTime > 3000) {
        console.log("停止按钮: 状态可能过期，先请求更新");
        requestStateUpdate();
        
        // 延迟处理点击，等待状态更新
        setTimeout(() => {
            handleStopClick();
        }, 300);
        return;
    }
    
    // 立即处理停止
    handleStopClick();
});

// 停止按钮点击处理
function handleStopClick() {
    // 只有在播放中、暂停或导航状态才能停止
    if (currentState.readingState !== 'reading' && 
        currentState.readingState !== 'paused' && 
        currentState.readingState !== 'navigating') {
        console.log("当前状态不能停止:", currentState.readingState);
        return;
    }
    
    try {
        // 1. 首先直接调用stop
        chrome.tts.stop();
        
        // 2. 发送停止消息到后台
        chrome.runtime.sendMessage({ action: 'stopReading' }, (response) => {
            if (chrome.runtime.lastError) {
                console.error("停止朗读时出错:", chrome.runtime.lastError);
                showMessage(`停止朗读时出错: ${chrome.runtime.lastError.message}`, true);

                // 出错时再次尝试直接停止
                setTimeout(() => {
                    chrome.tts.stop();
                }, 100);
            } else {
                console.log("🛑 停止朗读响应:", response);
                console.log("🔄 播放状态已清除，当前页面缓存已清除，下次播放将重新解析");

                // 手动重置UI状态
                statusIndicatorEl.textContent = '已停止';
                statusIndicatorEl.className = 'status-indicator stopped';
                playPauseButton.classList.remove('playing');
                playPauseButton.title = "点击直接播放当前页";

                // 清除本地状态
                currentState.readingState = 'idle';
                currentState.currentReadingTabId = null;

                // 确保计时器被重置
                resetPlaybackTimer();
                
                // 发送一个额外的状态请求，确保UI与后台同步
                setTimeout(() => {
                    sendMessageToBackground({ action: 'getState' });
                    
                    // 最后一次检查是否真的停止了
                    chrome.tts.isSpeaking((speaking) => {
                        if (speaking) {
                            console.log("检测到TTS仍在播放，进行最后一次停止");
                            chrome.tts.stop();
                        }
                    });
                }, 300);
            }
        });
    } catch (error) {
        console.error("发送停止朗读消息时出错:", error);
        showMessage(`停止朗读时出错: ${error.message}`, true);
        // 出错时也尝试直接停止
        chrome.tts.stop();
    }
}

// 实时语速调整 - input事件用于实时预览和调整
speedSlider.addEventListener('input', () => {
    const speed = parseFloat(speedSlider.value).toFixed(1);
    speedValueEl.textContent = `${speed}x`;

    // 实时调整语速（在播放状态下）
    if (currentState.readingState === 'reading') {
        // 使用防抖机制，避免过于频繁的调整
        clearTimeout(speedSlider.adjustTimeout);
        speedSlider.adjustTimeout = setTimeout(() => {
            const safeSpeed = Math.max(0.5, Math.min(5.0, parseFloat(speed)));
            console.log("实时调整语速到:", safeSpeed);

            try {
                chrome.runtime.sendMessage({ action: 'setSpeed', speed: safeSpeed }, (response) => {
                    if (chrome.runtime.lastError) {
                        console.error("实时调整语速时出错:", chrome.runtime.lastError);
                    } else {
                        console.log("实时语速调整成功:", response);
                    }
                });
            } catch (error) {
                console.error("发送实时语速调整消息时出错:", error);
            }
        }, 200); // 200ms防抖延迟
    }
});

// 语速调整完成事件 - change事件用于最终确认和同步
speedSlider.addEventListener('change', () => {
    const speed = parseFloat(speedSlider.value);
    console.log("语速调整完成:", speed);

    // 限制语速范围，和背景脚本保持一致
    const safeSpeed = Math.max(0.5, Math.min(5.0, speed));
    if (safeSpeed !== speed) {
        speedSlider.value = safeSpeed;
        speedValueEl.textContent = `${safeSpeed.toFixed(1)}x`;
        console.log(`调整语速到安全范围: ${safeSpeed}`);
    }

    // 同步设置面板的语速滑块
    if (overlaySpeedSlider) {
        overlaySpeedSlider.value = safeSpeed;
        overlaySpeedValue.textContent = `${safeSpeed.toFixed(1)}x`;
    }

    // 清除防抖计时器
    if (speedSlider.adjustTimeout) {
        clearTimeout(speedSlider.adjustTimeout);
        speedSlider.adjustTimeout = null;
    }

    // 最终确认语速设置
    try {
        chrome.runtime.sendMessage({ action: 'setSpeed', speed: safeSpeed }, (response) => {
            if (chrome.runtime.lastError) {
                console.error("最终确认语速时出错:", chrome.runtime.lastError);
                showMessage(`调整语速时出错: ${chrome.runtime.lastError.message}`, true);

                // 出错时请求状态更新
                setTimeout(() => requestStateUpdate(), 200);
            } else {
                console.log("语速最终确认成功:", response);
                
                // 检查是否中断了播放
                if (response && response.wasInterrupted === true) {
                    console.log("语速调整导致了播放中断，等待恢复...");
                    
                    // 显示临时状态
                    statusIndicatorEl.textContent = '正在恢复播放...';
                    statusIndicatorEl.className = 'status-indicator reading';
                    
                    // 等待状态恢复
                    setTimeout(() => {
                        requestStateUpdate();
                        
                        // 额外检查以确保状态一致
                        setTimeout(() => {
                            if (wasReading &&
                                currentState.readingState !== 'reading' &&
                                currentState.readingState !== 'navigating' &&
                                currentState.readingState !== 'changing_speed') {
                                console.log("检测到状态不一致，尝试恢复播放");

                                // 尝试恢复播放
                                chrome.runtime.sendMessage({ action: 'startReading' }, () => {
                                    setTimeout(() => requestStateUpdate(), 200);
                                });
                            } else if (wasReading && currentState.readingState === 'reading') {
                                // 确保UI显示正确的播放状态
                                console.log("✅ 语速调整完成，确保UI显示播放状态");
                                playPauseButton.classList.add('playing');
                                playPauseButton.title = "暂停播放";
                                statusIndicatorEl.textContent = translateState('reading');
                                statusIndicatorEl.className = 'status-indicator reading';
                            }
                        }, 500);
                    }, 300);
                } else {
                    // 无中断或非播放状态变化，简单更新UI
                    setTimeout(() => requestStateUpdate(), 200);
                }
            }
        });
    } catch (error) {
        console.error("发送语速调整消息时出错:", error);
        showMessage(`调整语速时出错: ${error.message}`, true);
        
        // 出错时请求状态更新
        setTimeout(() => requestStateUpdate(), 200);
    }
});

voiceSelect.addEventListener('change', () => {
    const selectedVoice = voiceSelect.value;
    console.log("Voice selected:", selectedVoice);
    try {
        chrome.runtime.sendMessage({ action: 'setVoice', payload: selectedVoice }, (response) => {
            if (chrome.runtime.lastError) {
                console.error("设置语音时出错:", chrome.runtime.lastError);
                showMessage(`设置语音时出错: ${chrome.runtime.lastError.message}`, true);
            } else {
                console.log("设置语音响应:", response);
            }
        });
    } catch (error) {
        console.error("发送设置语音消息时出错:", error);
        showMessage(`设置语音时出错: ${error.message}`, true);
    }
});

continuousReadingToggle.addEventListener('change', () => { 
    const enabled = continuousReadingToggle.checked;
    console.log("Continuous reading toggled:", enabled);
    try {
        chrome.runtime.sendMessage({ action: 'setContinuousReading', continuous: enabled }, (response) => {
            if (chrome.runtime.lastError) {
                console.error("设置连续阅读时出错:", chrome.runtime.lastError);
                showMessage(`设置连续阅读时出错: ${chrome.runtime.lastError.message}`, true);
            } else {
                console.log("设置连续阅读响应:", response);
            }
        });
    } catch (error) {
        console.error("发送设置连续阅读消息时出错:", error);
        showMessage(`设置连续阅读时出错: ${error.message}`, true);
    }
});

decrementChapterBtn.addEventListener('click', () => {
    let currentValue = parseInt(chapterInput.value, 10);
    if (currentValue > 1) { 
        currentValue--;
        chapterInput.value = currentValue;
        console.log("Chapters decremented to:", currentValue);
        sendMessageToBackground({ action: 'setContinuousChapters', chapters: currentValue });
    }
});

incrementChapterBtn.addEventListener('click', () => {
    let currentValue = parseInt(chapterInput.value, 10);
    currentValue++;
    chapterInput.value = currentValue;
    console.log("Chapters incremented to:", currentValue);
    sendMessageToBackground({ action: 'setContinuousChapters', chapters: currentValue });
});

chapterInput.addEventListener('input', () => {
    let currentValue = parseInt(chapterInput.value, 10);
    if (isNaN(currentValue) || currentValue < 1) {
        currentValue = 1;
        chapterInput.value = currentValue;
    }
    console.log("Chapters input changed to:", currentValue);
    sendMessageToBackground({ action: 'setContinuousChapters', chapters: currentValue });
});

// --- 新增的语音设置事件监听器 ---
// 语言过滤标签切换
voiceFilterTabs.forEach(tab => {
    tab.addEventListener('click', () => {
        // 移除所有标签的active类
        voiceFilterTabs.forEach(t => t.classList.remove('active'));
        // 为当前标签添加active类
        tab.classList.add('active');
        // 重新渲染语音列表
        renderAvailableVoices();
    });
});

// 性别过滤切换
genderCheckboxes.forEach(checkbox => {
    checkbox.addEventListener('change', () => {
        renderAvailableVoices();
    });
});

// 语音搜索
voiceSearchInput.addEventListener('input', () => {
    renderAvailableVoices();
});

// 保存语音设置
saveVoicesSettingsBtn.addEventListener('click', () => {
    // 获取选中的默认语音
    const selectedVoice = defaultVoiceSelect.value;
    
    // 更新主界面的语音选择器
    voiceSelect.value = selectedVoice;
    
    // 触发change事件，确保值更改被检测到
    voiceSelect.dispatchEvent(new Event('change'));
    
    // 发送设置到后台
    sendMessageToBackground({ action: 'setVoice', payload: selectedVoice });
    
    // 保存收藏的语音列表
    saveFavoriteVoices();
    
    // 关闭设置面板
    hideSettingsOverlay();
    
    // 显示保存成功消息
    showMessage("语音设置已保存");
    setTimeout(hideMessage, 2000);
});

// 默认语音选择变化时更新语音列表选中状态
defaultVoiceSelect.addEventListener('change', () => {
    renderAvailableVoices();
});

const MAX_DURATION_SECONDS = 12 * 60 * 60; 

let isDraggingDuration = false;

function formatTime(totalSeconds) {
    totalSeconds = Math.max(0, Math.floor(totalSeconds));
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
}

function formatTimeForBubble(totalSeconds) {
    totalSeconds = Math.max(0, Math.floor(totalSeconds));
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    
    if (hours > 0) {
        return `${hours}小时${minutes}分钟`;
    } else {
        return `${minutes}分钟`;
    }
}

function updateDurationUI(percentage) {
    percentage = Math.max(0, Math.min(100, percentage));
    progressBar.style.width = `${percentage}%`;
    const totalSeconds = (percentage / 100) * MAX_DURATION_SECONDS;
    // 更新时间气泡位置和内容
    const bubblePosition = Math.max(percentage, 0); // 确保气泡不会超出左边界
    timeBubbleEl.style.left = `${bubblePosition}%`;
    timeBubbleEl.textContent = formatTimeForBubble(totalSeconds);
    
    // 当前选择的时间显示在左侧
    currentTimeEl.textContent = formatTime(totalSeconds);
    // 右侧始终显示最大时长
    totalTimeEl.textContent = formatTime(MAX_DURATION_SECONDS);
}

function handleDurationPointerEvent(event) {
    const rect = progressBarContainer.getBoundingClientRect();
    const offsetX = event.clientX - rect.left;
    const width = progressBarContainer.offsetWidth;
    let percentage = (offsetX / width) * 100;
    percentage = Math.max(0, Math.min(100, percentage)); 
    updateDurationUI(percentage);
    return percentage;
}

function onPointerMove(event) {
    if (!isDraggingDuration) return;
    event.preventDefault(); 
    progressBarContainer.classList.add('dragging');
    handleDurationPointerEvent(event);
}

function onPointerUp(event) {
    if (!isDraggingDuration) return;
    isDraggingDuration = false;
    progressBarContainer.classList.remove('dragging');
    document.removeEventListener('pointermove', onPointerMove);
    document.removeEventListener('pointerup', onPointerUp);

    const percentage = parseFloat(progressBar.style.width) || 0;
    const finalDurationSeconds = Math.floor((percentage / 100) * MAX_DURATION_SECONDS);
    console.log(`Duration set to: ${formatTime(finalDurationSeconds)} (${finalDurationSeconds}s)`);
    sendMessageToBackground({ action: 'setReadingDuration', payload: finalDurationSeconds });
}

progressBarContainer.addEventListener('pointerdown', (event) => {
    isDraggingDuration = true;
    progressBarContainer.style.cursor = 'grabbing'; 
    progressBarContainer.classList.add('dragging');
    handleDurationPointerEvent(event);
    
    document.addEventListener('pointermove', onPointerMove);
    document.addEventListener('pointerup', onPointerUp);
});

progressBarContainer.addEventListener('pointerenter', () => {
    if (!isDraggingDuration) {
        progressBarContainer.style.cursor = 'grab';
    }
});
progressBarContainer.addEventListener('pointerleave', () => {
    if (!isDraggingDuration) {
        progressBarContainer.style.cursor = 'default';
    }
});

let playbackTimer = null;
let playbackStartTime = 0;
let playbackElapsedTime = 0;

// 格式化播放时长
function formatPlaybackTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
}

// 开始计时器
function startPlaybackTimer() {
    if (playbackTimer) {
        clearInterval(playbackTimer);
    }
    
    playbackStartTime = Date.now();
    playbackTimer = setInterval(() => {
        const currentTime = Date.now();
        const elapsedSeconds = Math.floor((currentTime - playbackStartTime) / 1000) + playbackElapsedTime;
        playbackTimerEl.textContent = formatPlaybackTime(elapsedSeconds);
    }, 1000);
}

// 暂停计时器
function pausePlaybackTimer() {
    if (playbackTimer) {
        clearInterval(playbackTimer);
        playbackTimer = null;

        // 保存已经过的时间，以便恢复时继续计时
        const currentTime = Date.now();
        playbackElapsedTime += Math.floor((currentTime - playbackStartTime) / 1000);
    }
}

// 更新剩余播放时间显示
function updateRemainingTimeDisplay(remainingSeconds, readingState) {
    if (!remainingTimeDisplayEl || !remainingTimeEl) return;

    // 只在播放或暂停状态时显示剩余时间
    if (readingState === 'reading' || readingState === 'paused') {
        remainingTimeDisplayEl.style.display = 'flex';
        remainingTimeEl.textContent = formatPlaybackTime(remainingSeconds);

        // 根据剩余时间改变颜色
        if (remainingSeconds <= 300) { // 5分钟以内
            remainingTimeEl.style.color = '#ff6b6b'; // 红色警告
        } else if (remainingSeconds <= 600) { // 10分钟以内
            remainingTimeEl.style.color = '#ffa726'; // 橙色提醒
        } else {
            remainingTimeEl.style.color = '#E1AD5B'; // 正常金色
        }
    } else {
        remainingTimeDisplayEl.style.display = 'none';
    }
}

function sendMessageToBackground(message) {
    if (!chrome || !chrome.runtime) {
        console.error("Chrome runtime is not available. Cannot send message.");
        showMessage("错误: 无法连接到扩展核心。", true);
        return;
    }
    
    try {
        chrome.runtime.sendMessage(message, (response) => {
            if (chrome.runtime.lastError) {
                console.error("Error sending message to background:", chrome.runtime.lastError);
                showMessage(`错误: ${chrome.runtime.lastError.message}`, true);
                
                // 如果是获取状态的请求失败，可能意味着连接问题，尝试稍后重试
                if (message.action === 'getState') {
                    console.log("状态请求失败，30秒后重试...");
                    setTimeout(() => {
                        requestStateUpdate();
                    }, 30000);
                }
            } else {
                console.log("Response from background:", response);
                if (response && response.status) {
                     // Example: showMessage(`状态: ${response.status}`);
                }
                if (response && response.newState) {
                    updateUI(response.newState);
                }
            }
        });
    } catch (e) {
        console.error("发送消息时出错:", e);
        showMessage(`发送消息错误: ${e.message}`, true);
        
        // 尝试在错误后短暂延迟后重连
        if (message.action === 'getState') {
            setTimeout(() => {
                console.log("尝试在错误后恢复连接...");
                requestStateUpdate();
            }, 5000);
        }
    }
}

function updateUI(state) {
    console.log("更新UI状态:", JSON.stringify(state));
    // 确保状态和必要属性存在
    if (!state || typeof state.readingState === 'undefined') {
        console.warn("updateUI收到无效或不完整的状态:", JSON.stringify(state));
        return; 
    }

    // 记录状态更新时间
    lastStateUpdateTime = Date.now();
    
    console.log("更新UI - 读取状态:", state.readingState);
    console.log("更新UI - 翻译状态:", translateState(state.readingState));
    
    // 记录之前的状态，用于比较变化
    const previousState = {...currentState};
    // 更新当前状态
    currentState = {...state};
    
    // 特殊处理导航状态 - 确保过渡到下一章时UI保持一致
    if (state.readingState === 'navigating') {
        console.log("检测到导航状态，保持UI显示为播放中");
        // 在导航状态时保持UI显示为播放中，而不是中断用户体验
        
        // 确保按钮状态正确 - 显示暂停图标
        playPauseButton.classList.add('playing');
        playPauseButton.title = "暂停播放";
        playPauseButton.disabled = false;
        stopBtn.disabled = false;
        
        // 更新状态指示器
        statusIndicatorEl.textContent = translateState('navigating');
        statusIndicatorEl.className = 'status-indicator reading'; // 使用reading样式
        
        // 禁用章节选择和连续阅读开关
        continuousReadingToggle.disabled = true;
        chapterInput.disabled = true;
        decrementChapterBtn.disabled = true;
        incrementChapterBtn.disabled = true;
        document.getElementById('setting-locked-hint').style.display = 'block';
        
        // 确保计时器状态正确
        if (previousState.readingState === 'reading' && playbackTimer) {
            // 保持计时器运行
        } else if (!playbackTimer) {
            // 如果计时器未运行，启动它
            startPlaybackTimer();
        }
        
        // 不进行完整UI更新，保持简单处理，返回
        return;
    }
    
    // 特殊处理语速调整状态 - 确保调整语速时UI保持一致
    if (state.readingState === 'changing_speed') {
        console.log("检测到语速调整状态，保持UI显示为播放中");
        
        // 确保按钮状态正确 - 显示暂停图标
        playPauseButton.classList.add('playing');
        playPauseButton.title = "暂停播放";
        
        // 临时禁用播放/暂停按钮，防止用户干扰，但保持暂停外观
        playPauseButton.disabled = true;
        stopBtn.disabled = true;
        
        // 更新状态指示器但保持reading样式
        statusIndicatorEl.textContent = '调整语速中...';
        statusIndicatorEl.className = 'status-indicator reading';
        
        // 禁用章节选择和连续阅读开关
        continuousReadingToggle.disabled = true;
        chapterInput.disabled = true;
        decrementChapterBtn.disabled = true;
        incrementChapterBtn.disabled = true;
        document.getElementById('setting-locked-hint').style.display = 'block';
        
        // 确保计时器状态正确
        if (previousState.readingState === 'reading' && playbackTimer) {
            // 保持计时器运行
        } else if (!playbackTimer) {
            // 如果计时器未运行，启动它
            startPlaybackTimer();
        }
        
        // 短时间后重新启用按钮
        setTimeout(() => {
            playPauseButton.disabled = false;
            stopBtn.disabled = false;
            
            // 再次请求状态更新确保与后台一致
            requestStateUpdate();
        }, 1000);
        
        // 不进行完整UI更新，保持简单处理，返回
        return;
    }
    
    // 检查是否应该自动开始播放（页面刷新后且内容已解析成功）
    if (pendingAutoPlay && state.article && state.article.textContent && 
        state.readingState !== 'reading' && state.readingState !== 'paused') {
        // 检查是否真的是由播放按钮发起的刷新
        chrome.storage.session.get(['autoRefreshForPlay'], (data) => {
            if (data.autoRefreshForPlay) {
                console.log("检测到播放按钮标记的自动刷新，开始播放");
                pendingAutoPlay = false; // 重置标记，避免重复播放
                
                // 显示提示
                showMessage("解析成功，开始播放...", false);

                // 稍微延迟一下，确保UI状态已完全更新
                setTimeout(() => {
                    startReadingFromBeginning();
                }, 300);
            } else {
                console.log("虽然有pendingAutoPlay标记，但没有检测到播放按钮的刷新标记，不自动播放");
                pendingAutoPlay = false; // 重置标记，避免未来错误触发
            }
        });
    }
    
    // 更新状态指示器
    if (state.readingState === 'reading') {
        statusIndicatorEl.textContent = '朗读中';
        statusIndicatorEl.className = 'status-indicator reading';
        
        // 仅当之前没有计时器运行或状态发生变化时，才启动计时器
        if (!playbackTimer || previousState.readingState !== 'reading') {
            console.log("启动或恢复播放计时器");
            startPlaybackTimer();
        }
        
        // 确保按钮状态正确 - 显示暂停图标
        playPauseButton.classList.add('playing');
        playPauseButton.title = "暂停播放";
        playPauseButton.disabled = false;
        stopBtn.disabled = false;
        
        // 禁用章节选择和连续阅读开关
        continuousReadingToggle.disabled = true;
        chapterInput.disabled = true;
        decrementChapterBtn.disabled = true;
        incrementChapterBtn.disabled = true;
        document.getElementById('setting-locked-hint').style.display = 'block';
    } else {
        let statusText = translateState(state.readingState);
        if (state.isBackgroundPlaying && state.readingState === 'reading') {
            statusText += ' (后台播放)';
        }

        // 如果是暂停状态，显示当前位置信息
        if (state.readingState === 'paused' && state.currentPosition) {
            statusText += ` (位置: ${state.currentPosition})`;
        }

        statusIndicatorEl.textContent = statusText;
        statusIndicatorEl.className = `status-indicator ${state.readingState || 'idle'}`;
        
        if (state.readingState === 'paused') {
            // 暂停状态 - 确保UI状态正确
            pausePlaybackTimer();
            
            // 显示播放图标（表示点击将继续）
            playPauseButton.classList.remove('playing');
            playPauseButton.title = "继续播放当前页";
            playPauseButton.disabled = false;
            stopBtn.disabled = false;
        } else if (state.readingState === 'idle' || state.readingState === 'stopped') {
            // 停止/空闲状态 - 重置计时器
            resetPlaybackTimer();
            
            // 显示播放图标
            playPauseButton.classList.remove('playing');
            playPauseButton.title = "点击直接播放当前页";
            playPauseButton.disabled = state.readingState === 'loading';
            stopBtn.disabled = true; // 在空闲状态下禁用停止按钮
        } else if (state.readingState === 'waiting_refresh') {
            // 等待刷新重新解析状态
            pausePlaybackTimer();
            
            // 显示加载状态并禁用按钮
            playPauseButton.classList.remove('playing');
            playPauseButton.title = "刷新中...";
            playPauseButton.disabled = true;
            stopBtn.disabled = false; // 允许停止
        }
        
        // 启用/禁用章节选择和连续阅读开关（除了在暂停状态或等待刷新状态）
        const isDisabled = state.readingState === 'paused' || state.readingState === 'waiting_refresh';
        continuousReadingToggle.disabled = isDisabled;
        chapterInput.disabled = isDisabled;
        decrementChapterBtn.disabled = isDisabled;
        incrementChapterBtn.disabled = isDisabled;
        document.getElementById('setting-locked-hint').style.display = isDisabled ? 'block' : 'none';
    }

    // 更新语速设置
    if (state.currentSpeed !== parseFloat(speedSlider.value)) {
        speedSlider.value = state.currentSpeed;
        speedValueEl.textContent = `${state.currentSpeed.toFixed(1)}x`;
    }
    
    // 更新语音选择
    if (state.currentVoice !== voiceSelect.value) {
         if (availableVoices.length > 0) {
            voiceSelect.value = state.currentVoice || ''; 
            if (voiceSelect.selectedIndex === -1) voiceSelect.selectedIndex = 0; 
         }
    }
    
    // 更新连续阅读开关
    if (state.continuousReading !== continuousReadingToggle.checked) { 
        continuousReadingToggle.checked = state.continuousReading;
    }

    // 更新章节数设置
    if (state.continuousChapters && state.continuousChapters !== parseInt(chapterInput.value, 10)) {
        chapterInput.value = state.continuousChapters;
    }

    // 更新阅读时长
    if (typeof state.readingDuration === 'number') {
        const percentage = (state.readingDuration / MAX_DURATION_SECONDS) * 100;
        updateDurationUI(percentage);
    }

    // 更新剩余播放时间显示
    if (typeof state.remainingPlaybackTime === 'number') {
        updateRemainingTimeDisplay(state.remainingPlaybackTime, state.readingState);
    } else {
        // 隐藏剩余时间显示
        if (remainingTimeDisplayEl) {
            remainingTimeDisplayEl.style.display = 'none';
        }
    }

    // 仅当状态发生变化时才更新文章预览（提高性能）
    if (!previousState.article || !state.article || 
        previousState.article.textContent !== state.article.textContent) {
        // 更新文章预览 - 显示第一句和最后一句
        if (state.article && state.article.textContent) {
            console.log("更新文章预览");
            
            // 获取文章内容
            const articleText = state.article.textContent;
            
            // 简单地取文章的前一部分和后一部分
            const maxPreviewLength = 100; // 用于调试的较长预览
            
            // 截取文章的前10个字符作为第一句
            const firstPart = articleText.substring(0, 10).trim();
            
            // 截取文章的最后10个字符作为最后一句
            const lastPart = articleText.length > 10 
                ? articleText.substring(articleText.length - 10).trim() 
                : "";
            
            // 更新DOM元素
            startSentenceEl.textContent = firstPart || "内容加载中...";
            endSentenceEl.textContent = lastPart || "内容加载中...";
            
            console.log("文章预览已更新");
        } else {
            // 如果没有文章内容，显示加载中
            startSentenceEl.textContent = "内容加载中...";
            endSentenceEl.textContent = "内容加载中...";
            console.log("没有文章内容可用");
        }
    }

    // 更新当前章节标题（同时更新简略版和详细版）
    if (state.currentChapterTitle) {
        // 处理标题，遇到下划线就截断
        const processedTitle = processChapterTitle(state.currentChapterTitle);
        
        // 截取处理后的标题前30个字符作为简略版，如果超过则显示...
        const briefTitle = processedTitle.length > 30 
            ? processedTitle.substring(0, 30) + '...' 
            : processedTitle;
            
        // 如果是后台播放，添加标识
        if (state.isBackgroundPlaying) {
            currentChapterTitleBrief.textContent = `${briefTitle} (后台)`;
            currentChapterTitleEl.textContent = `${processedTitle} (后台播放)`;
        } else {
            currentChapterTitleBrief.textContent = briefTitle;
            currentChapterTitleEl.textContent = processedTitle;
        }
    } else {
        currentChapterTitleBrief.textContent = "当前章节";
        currentChapterTitleEl.textContent = "当前章节";
    }

    // 更新待播放章节信息（如果有预加载的章节）
    const nextChapterEl = document.getElementById('next-chapter');
    if (nextChapterEl) {
        console.log("预加载状态:", state.nextChapterInfo);
        
        if (state.nextChapterInfo && state.nextChapterInfo.isPreloaded && state.nextChapterInfo.title) {
            // 处理下一章节标题，保持与当前章节标题处理方式一致
            const processedNextTitle = processChapterTitle(state.nextChapterInfo.title);
            
            // 根据是否完全预加载显示不同的提示
            if (state.nextChapterInfo.isFullyPreloaded) {
                nextChapterEl.textContent = `${processedNextTitle} ✓`;
                nextChapterEl.title = "已完全预加载，点击章节播放按钮后可立即播放";
                console.log("显示完全预加载章节:", processedNextTitle);
            } else {
                nextChapterEl.textContent = processedNextTitle;
                nextChapterEl.title = "已预加载链接，点击章节播放按钮后需要加载内容";
                console.log("显示预加载章节:", processedNextTitle);
            }
            
            // 添加点击事件处理 - 点击章节标题可直接跳转到下一章
            nextChapterEl.onclick = function() {
                // 如果当前正在播放，则先要停止
                if (currentState.readingState === 'reading' || currentState.readingState === 'paused') {
                    // 先停止当前播放
                    sendMessageToBackground({ action: 'stopReading' });
                    
                    // 等待一小段时间确保停止成功，然后再请求下一章
                    setTimeout(() => {
                        console.log("请求导航到下一章");
                        sendMessageToBackground({ action: 'navigateToNextChapter' });
                    }, 300);
                } else {
                    // 如果当前未播放，直接请求下一章
                    console.log("请求导航到下一章");
                    sendMessageToBackground({ action: 'navigateToNextChapter' });
                }
            };
            
            // 自动展开章节详情，确保用户能看到待播放信息
            const chapterDetails = document.querySelector('.chapter-details');
            if (chapterDetails && chapterDetails.style.display === 'none') {
                chapterDetails.style.display = 'block';
                document.querySelector('.toggle-chapter-card').textContent = '▲';
            }
        } else {
            // 如果没有预加载信息，显示暂无内容
            nextChapterEl.textContent = "暂无预加载内容";
            nextChapterEl.title = "";
            nextChapterEl.onclick = null; // 移除点击事件
            console.log("无预加载信息，显示默认文本");
        }
    }

    // 显示错误或消息
    if (state.error) {
        showMessage(state.error, true);
    } else if (state.message) {
        showMessage(state.message);
    } else {
        hideMessage();
    }

    // 更新播放状态信息显示
    updatePlayingStatusDisplay(state);
}

// 更新播放状态信息显示
function updatePlayingStatusDisplay(state) {
    if (!playingContentInfo) return;

    // 如果正在播放或暂停，显示播放信息
    if (state.readingState === 'reading' || state.readingState === 'paused') {
        // 优先使用文章信息
        if (state.article && state.article.title) {
            updatePlayingContentInfo(state.article.title, state.article.url || getCurrentTabURL());
        } else if (currentPlayingURL) {
            // 如果没有文章信息但有当前播放URL，保持显示
            // 不更新，保持当前显示的信息
        } else {
            // 尝试获取当前标签页信息
            getCurrentTabInfo().then(tabInfo => {
                if (tabInfo) {
                    updatePlayingContentInfo(tabInfo.title, tabInfo.url);
                }
            }).catch(error => {
                console.warn("获取当前标签页信息失败:", error);
            });
        }
    } else if (state.readingState === 'idle' || state.readingState === 'stopped') {
        // 播放停止时隐藏播放信息
        hidePlayingContentInfo();
    }
}

// 获取当前标签页URL
function getCurrentTabURL() {
    return new Promise((resolve) => {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (tabs && tabs.length > 0) {
                resolve(tabs[0].url);
            } else {
                resolve(null);
            }
        });
    });
}

// 获取当前标签页信息
function getCurrentTabInfo() {
    return new Promise((resolve) => {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (tabs && tabs.length > 0) {
                resolve({
                    title: tabs[0].title,
                    url: tabs[0].url,
                    id: tabs[0].id
                });
            } else {
                resolve(null);
            }
        });
    });
}

// 添加translateState函数中对waiting_refresh状态的翻译
function translateState(state) {
    const stateMap = {
        'idle': '空闲',
        'reading': '朗读中',
        'paused': '已暂停',
        'stopped': '已停止',
        'loading': '加载中',
        'navigating': '切换章节中',  // 确保正确翻译导航状态
        'waiting_refresh': '刷新中',
        'changing_speed': '调整语速中' // 添加对语速调整状态的翻译
    };
    return stateMap[state] || state;
}

function showMessage(message, isError = false) {
    if (!messageAreaEl || !footerEl) return; 
    messageAreaEl.textContent = message;
    messageAreaEl.classList.toggle('error-message', isError);
    footerEl.style.display = 'block'; 
}

function hideMessage() {
    if (!messageAreaEl || !footerEl) return; 
    messageAreaEl.textContent = '';
    messageAreaEl.classList.remove('error-message');
    footerEl.style.display = 'none'; 
}

function populateVoiceList(voices) {
    console.log("开始填充语音列表，收到的语音数量:", voices?.length || 0, voices);
    console.log("当前默认语音状态:", currentState.currentVoice);
    
    if (!voices || voices.length === 0) {
        console.warn("没有可用的语音或语音列表为空");
        voiceSelect.innerHTML = '<option value="">无可用语音</option>';
        defaultVoiceSelect.innerHTML = '<option value="">无可用语音</option>';
        
        // 清空语音列表
        renderEmptyVoiceLists();
        return;
    }

    try {
        // 保存语音列表
        availableVoices = Array.isArray(voices) ? voices : [];
        console.log("可用语音列表已保存，数量:", availableVoices.length);
        
        // 加载收藏的语音
        console.log("加载收藏的语音");
        loadFavoriteVoices();
        
        // 清空下拉列表
        voiceSelect.innerHTML = '';
        defaultVoiceSelect.innerHTML = '';
        
        // 添加默认选项到设置页面下拉框
        console.log("添加默认选项");
        const defaultOption = document.createElement('option');
        defaultOption.value = "";
        defaultOption.textContent = "默认/自动";
        defaultVoiceSelect.appendChild(defaultOption);
        
        // 按语言分组
        console.log("按语言分组语音");
        const voicesByLang = {};
        
        availableVoices.forEach(voice => {
            if (!voice.voiceName) {
                console.warn("语音对象缺少voiceName属性:", voice);
                return;
            }
            
            const langCode = voice.lang ? voice.lang.split('-')[0] : 'unknown';
            if (!voicesByLang[langCode]) {
                voicesByLang[langCode] = [];
            }
            voicesByLang[langCode].push(voice);
        });
        
        // 语言分组计数
        console.log("语言分组计数:", Object.keys(voicesByLang).length);
        
        // 主界面只添加常用语音（默认和收藏）
        // 创建常用语音组（包括默认语音和收藏语音）
        const frequentGroup = document.createElement('optgroup');
        frequentGroup.label = "常用语音";
        let hasFrequentVoices = false;
        
        // 添加默认语音到常用组（如果存在）
        if (currentState.currentVoice) {
            console.log("正在添加默认语音到下拉框:", currentState.currentVoice);
            const defaultVoiceData = availableVoices.find(v => v.voiceName === currentState.currentVoice);
            if (defaultVoiceData) {
                const option = document.createElement('option');
                option.value = defaultVoiceData.voiceName;
                // 移除"Microsoft"前缀
                let displayName = defaultVoiceData.voiceName.replace(/^Microsoft\s+/i, '');
                // 使用更明显的标记符号标注默认语音，确保在所有浏览器中都能正确显示
                option.textContent = `【默认】${displayName}`;
                option.className = 'default-voice-option';
                frequentGroup.appendChild(option);
                hasFrequentVoices = true;
                console.log("已添加默认语音选项:", option.textContent, "类名:", option.className);
            } else {
                console.warn("未在可用语音中找到当前默认语音:", currentState.currentVoice);
            }
        } else {
            console.log("没有设置默认语音");
        }
        
        // 添加收藏的语音
        if (favoriteVoices && Array.isArray(favoriteVoices) && favoriteVoices.length > 0) {
            favoriteVoices.forEach(voiceName => {
                // 不重复添加已添加的默认语音
                if (!voiceName || voiceName === currentState.currentVoice) return;
                
                const voice = availableVoices.find(v => v.voiceName === voiceName);
                if (voice) {
                    const option = document.createElement('option');
                    option.value = voice.voiceName;
                    // 移除"Microsoft"前缀
                    let displayName = voice.voiceName.replace(/^Microsoft\s+/i, '');
                    option.textContent = `【收藏】${displayName}`;
                    option.className = 'favorite-voice-option';
                    frequentGroup.appendChild(option);
                    hasFrequentVoices = true;
                }
            });
        }
        
        // 如果有常用语音，添加到主界面选择器
        if (hasFrequentVoices) {
            voiceSelect.appendChild(frequentGroup);
        } else {
            // 如果没有常用语音，添加提示选项
            const noFavoriteOption = document.createElement('option');
            noFavoriteOption.value = "";
            noFavoriteOption.textContent = "未设置常用语音";
            noFavoriteOption.disabled = true;
            voiceSelect.appendChild(noFavoriteOption);
        }
        
        // 在设置页面下拉框中添加所有语音（按语言分组）
        for (const langCode in voicesByLang) {
            console.log(`处理${langCode}语言组，包含${voicesByLang[langCode].length}个语音`);
            const langGroup = document.createElement('optgroup');
            langGroup.label = getLangName(langCode);
            
            // 按名称排序语音
            voicesByLang[langCode].sort((a, b) => a.voiceName.localeCompare(b.voiceName));
            
            voicesByLang[langCode].forEach(voice => {
                // 确保voice对象有效
                if (!voice || !voice.voiceName) return;
                
                const option = document.createElement('option');
                option.value = voice.voiceName;
                // 移除"Microsoft"前缀
                let displayName = voice.voiceName.replace(/^Microsoft\s+/i, '');
                
                // 检查是否是默认语音或收藏语音，并相应地添加标记和类
                if (currentState.currentVoice && voice.voiceName === currentState.currentVoice) {
                    option.textContent = `【默认】${displayName}` + (voice.gender ? ` (${voice.gender})` : '');
                    option.className = 'default-voice-option';
                } else if (favoriteVoices && Array.isArray(favoriteVoices) && favoriteVoices.includes(voice.voiceName)) {
                    option.textContent = `【收藏】${displayName}` + (voice.gender ? ` (${voice.gender})` : '');
                    option.className = 'favorite-voice-option';
                } else {
                    option.textContent = `${displayName}` + (voice.gender ? ` (${voice.gender})` : '');
                }
                
                langGroup.appendChild(option);
            });

            // 只添加到设置页面下拉框
            // 不使用cloneNode，改为创建新的optgroup并递归复制选项
            const defaultGroup = document.createElement('optgroup');
            defaultGroup.label = langGroup.label;
            
            // 为defaultVoiceSelect中的选项设置和主下拉列表相同的样式
            voicesByLang[langCode].forEach(voice => {
                // 确保voice对象有效
                if (!voice || !voice.voiceName) return;
                
                const option = document.createElement('option');
                option.value = voice.voiceName;
                // 移除"Microsoft"前缀
                let displayName = voice.voiceName.replace(/^Microsoft\s+/i, '');
                
                // 检查是否是默认语音或收藏语音，并相应地添加标记和类
                if (currentState.currentVoice && voice.voiceName === currentState.currentVoice) {
                    option.textContent = `【默认】${displayName}` + (voice.gender ? ` (${voice.gender})` : '');
                    option.className = 'default-voice-option';
                } else if (favoriteVoices && Array.isArray(favoriteVoices) && favoriteVoices.includes(voice.voiceName)) {
                    option.textContent = `【收藏】${displayName}` + (voice.gender ? ` (${voice.gender})` : '');
                    option.className = 'favorite-voice-option';
                } else {
                    option.textContent = `${displayName}` + (voice.gender ? ` (${voice.gender})` : '');
                }
                
                defaultGroup.appendChild(option);
            });
            
            defaultVoiceSelect.appendChild(defaultGroup);
        }
        
        // 设置选中值
        if (currentState.currentVoice) {
            console.log("设置当前选中的语音:", currentState.currentVoice);
            voiceSelect.value = currentState.currentVoice;
            defaultVoiceSelect.value = currentState.currentVoice;
            
            // 触发change事件，确保值更改被检测到
            voiceSelect.dispatchEvent(new Event('change'));
            defaultVoiceSelect.dispatchEvent(new Event('change'));
            
            // 如果找不到匹配的选项，则使用第一个
            if (voiceSelect.selectedIndex === -1) {
                console.warn("下拉列表中找不到当前语音，使用第一个选项");
                voiceSelect.selectedIndex = 0;
            }
            if (defaultVoiceSelect.selectedIndex === -1) {
                defaultVoiceSelect.selectedIndex = 0;
            }
        }
        
        // 渲染语音列表
        console.log("渲染语音列表UI");
        renderFavoriteVoices();
        renderAvailableVoices();
        
        console.log("语音列表填充完成");
    } catch (error) {
        console.error("填充语音列表时出错:", error);
        // 显示错误
        renderEmptyVoiceLists("语音列表加载失败: " + error.message);
    }
}

// 渲染空的语音列表
function renderEmptyVoiceLists(message = "无可用语音") {
    console.log("渲染空语音列表:", message);
    favoriteVoicesList.innerHTML = '<div class="empty-favorites-message">尚未添加常用语音</div>';
    availableVoicesList.innerHTML = `<div class="loading-voices">${message}</div>`;
}

// 获取语言名称
function getLangName(langCode) {
    const langNames = {
        'zh': '中文',
        'en': '英文',
        'ja': '日文',
        'fr': '法文',
        'de': '德文',
        'es': '西班牙文',
        'it': '意大利文',
        'ru': '俄文',
        'ko': '韩文',
        'unknown': '未知语言'
    };
    return langNames[langCode] || langCode;
}

// --- Settings Overlay Functions ---
function showSettingsOverlay() {
    try {
        console.log("打开语音设置页面...");
        
        // 添加锁定机制，防止重复打开
        if (window.voiceSettingsOpening) {
            console.log("语音设置页面正在打开中，请稍候...");
            return;
        }
        
        window.voiceSettingsOpening = true;
        
        // 获取语音设置内容容器和浮层元素
        const voiceSettingsContainer = document.getElementById('voice-settings-container');
        const voiceSettingsOverlay = document.getElementById('voice-settings-overlay');
        
        // 显示浮层
        voiceSettingsOverlay.style.display = 'flex';
        
        // 加载语音设置CSS - 优化资源引用
        const linkId = 'voice-settings-styles';
        if (!document.getElementById(linkId)) {
            // 不再加载额外的CSS文件，而是使用内联样式覆盖必要的样式
            const overrideStyle = document.createElement('style');
            overrideStyle.textContent = `
                /* 语音设置浮层关键样式覆盖 */
                .voice-settings-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.75);
                    z-index: 2000;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    padding: 7.5px;
                    backdrop-filter: blur(3px);
                    -webkit-backdrop-filter: blur(3px);
                }

                .voice-settings-content {
                    width: calc(100% - 15px);
                    height: calc(100% - 15px);
                    border: 1px solid rgba(220, 220, 255, 0.85);
                }

                .filter-tags-container {
                    display: flex !important;
                    flex-wrap: wrap !important;
                    width: 100% !important;
                }

                #embedded-dialect-tags-container {
                    display: contents !important;
                }
            `;
            document.head.appendChild(overrideStyle);
            
            // 不需要再加载额外的CSS文件
            // 删除原有的CSS文件加载代码
            // const link = document.createElement('link');
            // link.rel = 'stylesheet';
            // link.href = chrome.runtime.getURL('src/ui/voiceSettings/styles.css');
            // link.id = linkId;
            // document.head.appendChild(link);
        }
        
        // 初始化浮层内容
        voiceSettingsContainer.innerHTML = `
            <div class="voice-settings-container">
                <div class="voice-settings-header">
                    <h2>语音设置</h2>
                    <button id="embedded-close-voice-settings-btn" class="close-btn" title="关闭">×</button>
                </div>
                <div class="card filter-card">
                    <div class="search-box">
                        <input type="text" id="embedded-voice-search-input" placeholder="搜索语音..." class="voice-search-input">
                    </div>
                    <div class="filter-tags-container">
                        <!-- 所有标签现在直接放在一个容器中 -->
                        <button class="filter-tag active" data-filter="all">全部</button>
                        <button class="filter-tag" data-filter="zh" data-type="language">中文</button>
                        <button class="filter-tag" data-filter="en" data-type="language">英文</button>
                        <button class="filter-tag" data-filter="ja" data-type="language">日文</button>
                        <button class="filter-tag" data-filter="fr" data-type="language">法文</button>
                        <button class="filter-tag" data-filter="de" data-type="language">德文</button>
                        <button class="filter-tag" data-filter="ru" data-type="language">俄文</button>
                        <button class="filter-tag" data-filter="ko" data-type="language">韩文</button>
                        <!-- 方言标签将直接添加到这个容器中，不再使用单独的div -->
                        <span id="embedded-dialect-tags-container"></span>
                    </div>
                </div>
                <div id="embedded-voices-list" class="voices-list">
                    <div id="embedded-loading-message" class="loading-message">加载中...</div>
                </div>
                <template id="embedded-voice-item-template">
                    <div class="voice-item">
                        <div class="priority-indicator" style="display: none;"></div>
                        <div class="voice-info">
                            <div class="voice-name"></div>
                            <div class="voice-tags"></div>
                        </div>
                        <div class="voice-actions">
                            <button class="preview-btn" title="试听">🔊</button>
                            <button class="favorite-btn" title="收藏">☆</button>
                            <button class="default-btn" title="设为默认">○</button>
                        </div>
                    </div>
                </template>
                <template id="embedded-empty-state-template">
                    <div class="empty-state">
                        <div class="empty-state-title"></div>
                        <div class="empty-state-message"></div>
                    </div>
                </template>
            </div>
        `;
        
        // 激活浮层
        setTimeout(() => {
            voiceSettingsOverlay.classList.add('active');
        }, 10);
        
        // 添加浮层内语音设置的功能
        setupEmbeddedVoiceSettings(voiceSettingsContainer);
        
        // 添加点击浮层背景关闭的事件
        voiceSettingsOverlay.addEventListener('click', (e) => {
            if (e.target === voiceSettingsOverlay) {
                hideVoiceSettingsOverlay();
            }
        });
        
        window.voiceSettingsOpening = false;
    } catch (error) {
        console.error("打开语音设置页面时出错:", error);
        window.voiceSettingsOpening = false;
        // 如果出错，回退到原有的内嵌设置面板
        showOriginalSettingsOverlay();
    }
}

// 设置内嵌语音设置功能
function setupEmbeddedVoiceSettings(container) {
    // 获取基本DOM元素
    const closeBtn = container.querySelector('#embedded-close-voice-settings-btn');
    const searchInput = container.querySelector('#embedded-voice-search-input');
    const voicesList = container.querySelector('#embedded-voices-list');
    const loadingMessage = container.querySelector('#embedded-loading-message');
    const filterTags = container.querySelectorAll('.filter-tag');
    const voiceItemTemplate = container.querySelector('#embedded-voice-item-template');
    const emptyStateTemplate = container.querySelector('#embedded-empty-state-template');
    
    // 状态变量
    let availableVoices = [];
    let favoriteVoices = [];
    let defaultVoice = '';
    let currentFilters = {
        language: [],
        dialect: []
    };
    let searchText = '';
    
    // 初始化功能
    initializeEmbeddedVoiceSettings();
    
    // 添加事件监听
    closeBtn.addEventListener('click', hideVoiceSettingsOverlay);
    searchInput.addEventListener('input', handleSearchInput);
    filterTags.forEach(tag => {
        tag.addEventListener('click', handleFilterTagClick);
    });
    
    // 初始化函数
    function initializeEmbeddedVoiceSettings() {
        console.log('初始化内嵌语音设置...');
        
        // 加载收藏的语音
        chrome.storage.local.get('favoriteVoices', (result) => {
            if (result.favoriteVoices) {
                favoriteVoices = result.favoriteVoices;
                console.log('加载收藏语音:', favoriteVoices);
            } else {
                // 尝试从localStorage加载
                const saved = localStorage.getItem('favoriteVoices');
                if (saved) {
                    try {
                        favoriteVoices = JSON.parse(saved);
                        console.log('从localStorage加载收藏语音:', favoriteVoices);
                    } catch (error) {
                        console.error('解析收藏语音时出错:', error);
                        favoriteVoices = [];
                    }
                }
            }
            
            // 加载默认语音
            chrome.storage.local.get('currentVoice', (result) => {
                if (result.currentVoice) {
                    defaultVoice = result.currentVoice;
                    console.log('加载默认语音:', defaultVoice);
                } else {
                    // 尝试从localStorage加载
                    const savedVoice = localStorage.getItem('defaultVoice');
                    if (savedVoice) {
                        defaultVoice = savedVoice;
                        console.log('从localStorage加载默认语音:', defaultVoice);
                    }
                }
                
                // 加载筛选标签状态
                chrome.storage.local.get('voiceFilters', (result) => {
                    if (result.voiceFilters) {
                        currentFilters = result.voiceFilters;
                        console.log('从chrome.storage加载筛选状态:', currentFilters);
                    } else {
                        // 尝试从localStorage加载
                        const savedFilters = localStorage.getItem('voiceFilters');
                        if (savedFilters) {
                            try {
                                currentFilters = JSON.parse(savedFilters);
                                console.log('从localStorage加载筛选状态:', currentFilters);
                            } catch (error) {
                                console.error('解析筛选状态时出错:', error);
                                currentFilters = { language: [], dialect: [] };
                            }
                        }
                    }
                    
                    // 获取可用语音
                    chrome.tts.getVoices((voices) => {
                        console.log('获取可用语音数量:', voices?.length || 0);
                        availableVoices = voices || [];
                        if (availableVoices.length === 0) {
                            showNoVoicesMessage();
                        } else {
                            // 生成方言标签（先生成再更新UI状态）
                            generateDialectTags();
                            
                            // 更新筛选标签UI
                            updateFilterTagsUI();
                            
                            // 渲染语音列表
                            renderVoicesList();
                            
                            console.log('语音设置初始化完成');
                        }
                    });
                });
            });
        });
    }
    
    // 更新筛选标签UI
    function updateFilterTagsUI() {
        // 首先清除所有标签的激活状态
        container.querySelectorAll('.filter-tag').forEach(tag => {
            tag.classList.remove('active');
        });
        
        // 如果没有选中任何筛选，则激活"全部"标签
        if (currentFilters.language.length === 0 && currentFilters.dialect.length === 0) {
            container.querySelector('.filter-tag[data-filter="all"]').classList.add('active');
            return;
        }
        
        // 激活已选中的语言标签
        currentFilters.language.forEach(lang => {
            const langTag = container.querySelector(`.filter-tag[data-filter="${lang}"]`);
            if (langTag) langTag.classList.add('active');
        });
        
        // 激活已选中的方言标签
        currentFilters.dialect.forEach(dialect => {
            const dialectTag = container.querySelector(`.filter-tag[data-filter="${dialect}"]`);
            if (dialectTag) dialectTag.classList.add('active');
        });
    }
    
    // 渲染语音列表
    function renderVoicesList() {
        // 对语音进行排序和筛选
        const sortedVoices = sortVoicesByPriority();
        const filteredVoices = filterVoices(sortedVoices);
        
        // 清空列表并隐藏加载提示
        voicesList.innerHTML = '';
        
        // 检查是否为空结果
        if (filteredVoices.length === 0) {
            const noResultsElement = createNoResultsState();
            voicesList.appendChild(noResultsElement);
            return;
        }
        
        // 渲染语音项
        filteredVoices.forEach((voice) => {
            const voiceElement = createVoiceElement(voice);
            voicesList.appendChild(voiceElement);
        });
    }
    
    // 按优先级对语音进行排序
    function sortVoicesByPriority() {
        return [...availableVoices].sort((a, b) => {
            // 默认语音置顶
            if (a.voiceName === defaultVoice) return -1;
            if (b.voiceName === defaultVoice) return 1;
            
            // 收藏语音次之
            const aIsFavorite = favoriteVoices.includes(a.voiceName);
            const bIsFavorite = favoriteVoices.includes(b.voiceName);
            
            if (aIsFavorite && !bIsFavorite) return -1;
            if (!aIsFavorite && bIsFavorite) return 1;
            
            // 同一优先级按名称排序
            return a.voiceName.localeCompare(b.voiceName);
        });
    }
    
    // 筛选语音
    function filterVoices(voices) {
        return voices.filter(voice => {
            // 搜索文本筛选
            if (searchText && !voice.voiceName.toLowerCase().includes(searchText)) {
                return false;
            }
            
            // 如果没有激活的筛选器，显示所有语音
            if (container.querySelector('.filter-tag[data-filter="all"].active') ||
                (currentFilters.language.length === 0 && 
                 currentFilters.dialect.length === 0)) {
                return true;
            }
            
            // 方言筛选（优先级高于语言筛选）
            if (currentFilters.dialect.length > 0) {
                const dialect = detectDialect(voice);
                if (dialect && currentFilters.dialect.includes(dialect.code)) {
                    return true; // 如果方言匹配，直接返回true，不需要再检查语言
                }
            }
            
            // 语言筛选（仅在没有匹配方言时检查）
            if (currentFilters.language.length > 0) {
                const voiceLanguage = voice.lang ? voice.lang.split('-')[0] : 'other';
                if (currentFilters.language.includes(voiceLanguage)) {
                    return true;
                }
            }
            
            // 如果前面的筛选都不匹配，返回false
            return false;
        });
    }
    
    // 创建语音元素
    function createVoiceElement(voice) {
        const clone = voiceItemTemplate.content.cloneNode(true);
        const voiceItem = clone.querySelector('.voice-item');
        const voiceInfo = voiceItem.querySelector('.voice-info');
        const voiceName = voiceItem.querySelector('.voice-name');
        const priorityIndicator = voiceItem.querySelector('.priority-indicator');
        const previewBtn = voiceItem.querySelector('.preview-btn');
        const favoriteBtn = voiceItem.querySelector('.favorite-btn');
        const defaultBtn = voiceItem.querySelector('.default-btn');
        const voiceTags = voiceItem.querySelector('.voice-tags');
        
        // 设置数据属性和语音名称
        voiceItem.dataset.voiceName = voice.voiceName;
        
        // 移除"Microsoft"前缀
        let displayName = voice.voiceName.replace(/^Microsoft\s+/i, '');
        voiceName.textContent = displayName;
        
        // 检查是否为优先语音
        const isDefault = voice.voiceName === defaultVoice;
        const isFavorite = favoriteVoices.includes(voice.voiceName);
        
        if (isDefault || isFavorite) {
            voiceItem.classList.add('priority');
            priorityIndicator.style.display = 'inline';
        }
        
        // 设置按钮状态
        if (isFavorite) {
            favoriteBtn.textContent = '★';
            favoriteBtn.title = '取消收藏';
            favoriteBtn.classList.add('favorite');
        }
        
        if (isDefault) {
            defaultBtn.textContent = '✓';
            defaultBtn.title = '当前默认';
            defaultBtn.classList.add('default');
        }
        
        // 添加标签
        if (voice.lang) {
            const langCode = voice.lang.split('-')[0];
            const langName = getLangName(langCode);
            const tag = document.createElement('span');
            tag.className = 'voice-tag';
            tag.textContent = langName;
            voiceTags.appendChild(tag);
        }
        
        // 添加事件监听器
        previewBtn.addEventListener('click', () => previewVoice(voice.voiceName));
        favoriteBtn.addEventListener('click', () => toggleFavorite(voice.voiceName));
        defaultBtn.addEventListener('click', () => setAsDefault(voice.voiceName));
        
        return voiceItem;
    }
    
    // 生成方言标签
    function generateDialectTags() {
        const dialectsContainer = container.querySelector('#embedded-dialect-tags-container');
        dialectsContainer.innerHTML = '';
        
        // 获取所有可用方言
        const availableDialects = new Set();
        availableVoices.forEach(voice => {
            const dialect = detectDialect(voice);
            if (dialect) {
                availableDialects.add(dialect.code);
            }
        });
        
        // 直接将方言标签添加到容器
        availableDialects.forEach(dialectCode => {
            const dialectName = getDialectName(dialectCode);
            const dialectTag = document.createElement('button');
            dialectTag.className = 'filter-tag';
            dialectTag.dataset.filter = dialectCode;
            dialectTag.dataset.type = 'dialect';
            dialectTag.textContent = dialectName;
            
            // 检查是否已选中此方言
            if (currentFilters.dialect.includes(dialectCode)) {
                dialectTag.classList.add('active');
            }
            
            dialectTag.addEventListener('click', (event) => {
                toggleDialectFilter(dialectCode, dialectTag);
                event.stopPropagation(); // 防止事件冒泡到父元素
            });
            
            // 将标签插入到span容器之前，使其直接显示在filter-tags-container中
            dialectsContainer.parentNode.insertBefore(dialectTag, dialectsContainer);
        });
    }
    
    // 检测方言
    function detectDialect(voice) {
        const name = voice.voiceName.toLowerCase();
        const lang = voice.lang || '';
        
        // 中文方言检测
        if (lang.startsWith('zh')) {
            if (lang === 'zh-HK' || lang === 'zh-yue' || 
                name.includes('粤语') || name.includes('广东') || 
                name.includes('香港') || name.includes('cantonese')) {
                return { code: 'yue', name: '粤语' };
            }
            
            if (lang === 'zh-TW' || name.includes('台湾') || 
                name.includes('taiwan')) {
                return { code: 'tw', name: '台湾国语' };
            }
        }
        
        // 英语区域变体
        if (lang.startsWith('en')) {
            if (lang === 'en-US' || name.includes('american')) {
                return { code: 'us', name: '美式英语' };
            }
            if (lang === 'en-GB' || name.includes('british')) {
                return { code: 'gb', name: '英式英语' };
            }
            if (lang === 'en-AU' || name.includes('australia')) {
                return { code: 'au', name: '澳洲英语' };
            }
            if (lang === 'en-IN' || name.includes('india') || name.includes('indian')) {
                return { code: 'in', name: '印度英语' };
            }
        }
        
        return null;
    }
    
    // 语言名称
    function getLangName(langCode) {
        const langNames = {
            'zh': '中文',
            'en': '英文',
            'ja': '日文',
            'fr': '法文',
            'de': '德文',
            'es': '西班牙文',
            'it': '意大利文',
            'ru': '俄文',
            'ko': '韩文',
            'other': '其他语言'
        };
        return langNames[langCode] || langCode;
    }
    
    // 方言名称
    function getDialectName(dialectCode) {
        const dialectNames = {
            'yue': '粤语',
            'tw': '台湾国语',
            'us': '美式英语',
            'gb': '英式英语',
            'au': '澳洲英语',
            'in': '印度英语'
        };
        return dialectNames[dialectCode] || dialectCode;
    }
    
    // 创建空状态
    function createEmptyState(title, message) {
        const clone = emptyStateTemplate.content.cloneNode(true);
        const emptyState = clone.querySelector('.empty-state');
        const titleEl = emptyState.querySelector('.empty-state-title');
        const messageEl = emptyState.querySelector('.empty-state-message');
        
        titleEl.textContent = title;
        messageEl.textContent = message;
        
        return emptyState;
    }
    
    // 创建无结果状态
    function createNoResultsState() {
        return createEmptyState(
            '未找到匹配的语音',
            '尝试其他关键词或减少筛选条件'
        );
    }
    
    // 显示无语音消息
    function showNoVoicesMessage() {
        voicesList.innerHTML = '';
        const emptyState = createEmptyState(
            '未找到语音',
            '当前系统中没有可用的语音，请检查系统设置或使用第三方TTS引擎'
        );
        voicesList.appendChild(emptyState);
    }
    
    // 处理搜索输入
    function handleSearchInput() {
        searchText = searchInput.value.toLowerCase().trim();
        renderVoicesList();
    }
    
    // 处理筛选标签点击
    function handleFilterTagClick(event) {
        const tag = event.currentTarget;
        const filter = tag.dataset.filter;
        const type = tag.dataset.type;
        
        if (filter === 'all') {
            clearAllFilters();
            tag.classList.add('active');
        } else if (type === 'language') {
            toggleLanguageFilter(filter, tag);
        } else if (type === 'dialect') {
            toggleDialectFilter(filter, tag);
        }
        
        renderVoicesList();
    }
    
    // 切换语言筛选
    function toggleLanguageFilter(langCode, tag) {
        console.log(`切换语言筛选: ${langCode}`);
        
        // 取消"全部"的选中状态
        container.querySelector('.filter-tag[data-filter="all"]').classList.remove('active');
        
        // 切换当前语言的选中状态
        const index = currentFilters.language.indexOf(langCode);
        if (index === -1) {
            // 添加语言筛选
            currentFilters.language.push(langCode);
            tag.classList.add('active');
            console.log(`添加语言筛选: ${langCode}，当前筛选:`, currentFilters);
        } else {
            // 移除语言筛选
            currentFilters.language.splice(index, 1);
            tag.classList.remove('active');
            console.log(`移除语言筛选: ${langCode}，当前筛选:`, currentFilters);
        }
        
        // 如果没有任何筛选条件，激活"全部"标签
        if (currentFilters.language.length === 0 && currentFilters.dialect.length === 0) {
            container.querySelector('.filter-tag[data-filter="all"]').classList.add('active');
        }
        
        // 实时保存筛选状态
        saveFilterState();
        
        // 立即重新渲染语音列表
        renderVoicesList();
    }
    
    // 切换方言筛选
    function toggleDialectFilter(dialectCode, tag) {
        console.log(`切换方言筛选: ${dialectCode}`);
        
        // 取消"全部"的选中状态
        container.querySelector('.filter-tag[data-filter="all"]').classList.remove('active');
        
        // 切换当前方言的选中状态
        const index = currentFilters.dialect.indexOf(dialectCode);
        if (index === -1) {
            // 添加方言筛选
            currentFilters.dialect.push(dialectCode);
            tag.classList.add('active');
            console.log(`添加方言筛选: ${dialectCode}，当前筛选:`, currentFilters);
        } else {
            // 移除方言筛选
            currentFilters.dialect.splice(index, 1);
            tag.classList.remove('active');
            console.log(`移除方言筛选: ${dialectCode}，当前筛选:`, currentFilters);
        }
        
        // 如果没有任何筛选条件，激活"全部"标签
        if (currentFilters.language.length === 0 && currentFilters.dialect.length === 0) {
            container.querySelector('.filter-tag[data-filter="all"]').classList.add('active');
        }
        
        // 实时保存筛选状态
        saveFilterState();
        
        // 立即重新渲染语音列表
        renderVoicesList();
    }
    
    // 清除所有筛选条件
    function clearAllFilters() {
        console.log('清除所有筛选条件');
        
        // 重置筛选状态
        currentFilters = {
            language: [],
            dialect: []
        };
        
        // 更新所有标签UI状态
        container.querySelectorAll('.filter-tag').forEach(tag => {
            if (tag.dataset.filter === 'all') {
                tag.classList.add('active');
            } else {
                tag.classList.remove('active');
            }
        });
        
        // 实时保存筛选状态
        saveFilterState();
        
        // 立即重新渲染语音列表
        renderVoicesList();
        
        console.log('筛选条件已清除，当前筛选:', currentFilters);
    }
    
    // 保存筛选状态
    function saveFilterState() {
        try {
            // 保存到localStorage
            localStorage.setItem('voiceFilters', JSON.stringify(currentFilters));
            
            // 同时保存到chrome.storage.local
            chrome.storage.local.set({ voiceFilters: currentFilters });
            
            // 实时同步到其他页面
            chrome.runtime.sendMessage({
                action: 'updateVoiceOptions',
                voiceFilters: currentFilters
            });
        } catch (error) {
            console.error('保存筛选状态时出错:', error);
        }
    }

    // 试听语音
    function previewVoice(voiceName) {
        const voice = availableVoices.find(v => v.voiceName === voiceName);
        const langCode = voice.lang ? voice.lang.split('-')[0] : 'default';
        
        // 示例文本
        const sampleTexts = {
            'zh': '这是一段示例文本，用于测试语音效果。',
            'en': 'This is a sample text for testing voice quality.',
            'ja': 'これは音声品質をテストするためのサンプルテキストです。',
            'fr': 'Ceci est un exemple de texte pour tester la qualité de la voix.',
            'de': 'Dies ist ein Beispieltext zum Testen der Sprachqualität.',
            'default': 'Testing voice sample.'
        };
        
        const sampleText = sampleTexts[langCode] || sampleTexts.default;
        
        // 停止当前播放
        chrome.tts.stop();
        
        // 播放语音
        chrome.tts.speak(
            sampleText,
            {
                voiceName: voiceName,
                rate: 1.0
            }
        );
    }
    
    // 切换收藏状态
    function toggleFavorite(voiceName) {
        const voiceItem = container.querySelector(`.voice-item[data-voice-name="${voiceName}"]`);
        const favoriteBtn = voiceItem.querySelector('.favorite-btn');
        const isFavorite = favoriteVoices.includes(voiceName);
        
        if (isFavorite) {
            // 从收藏中移除
            const index = favoriteVoices.indexOf(voiceName);
            favoriteVoices.splice(index, 1);
            
            favoriteBtn.textContent = '☆';
            favoriteBtn.title = '收藏';
            favoriteBtn.classList.remove('favorite');
            
            // 如果不是默认语音，移除优先级样式
            if (voiceName !== defaultVoice) {
                voiceItem.classList.remove('priority');
                voiceItem.querySelector('.priority-indicator').style.display = 'none';
            }
        } else {
            // 添加到收藏
            favoriteVoices.push(voiceName);
            
            favoriteBtn.textContent = '★';
            favoriteBtn.title = '取消收藏';
            favoriteBtn.classList.add('favorite');
            voiceItem.classList.add('priority');
            voiceItem.querySelector('.priority-indicator').style.display = 'inline';
        }
        
        // 重新渲染以保持排序
        setTimeout(() => {
            renderVoicesList();
            // 自动保存收藏设置
            autoSaveSettings(isFavorite ? "已取消收藏" : "已添加到收藏");
        }, 300);
    }
    
    // 设置默认语音
    function setAsDefault(voiceName) {
        // 如果点击的是当前默认语音，不做操作
        if (voiceName === defaultVoice) {
            return;
        }
        
        // 获取原默认语音元素和新默认语音元素
        const oldDefaultItem = container.querySelector(`.voice-item[data-voice-name="${defaultVoice}"]`);
        const newDefaultItem = container.querySelector(`.voice-item[data-voice-name="${voiceName}"]`);
        
        // 更新默认语音
        defaultVoice = voiceName;
        
        // 如果原默认语音元素存在，更新其UI
        if (oldDefaultItem) {
            const oldDefaultBtn = oldDefaultItem.querySelector('.default-btn');
            oldDefaultBtn.textContent = '○';
            oldDefaultBtn.title = '设为默认';
            oldDefaultBtn.classList.remove('default');
            
            // 如果不是收藏语音，移除优先级样式
            if (!favoriteVoices.includes(oldDefaultItem.dataset.voiceName)) {
                oldDefaultItem.classList.remove('priority');
                oldDefaultItem.querySelector('.priority-indicator').style.display = 'none';
            }
        }
        
        // 更新新默认语音的UI
        const newDefaultBtn = newDefaultItem.querySelector('.default-btn');
        newDefaultBtn.textContent = '✓';
        newDefaultBtn.title = '当前默认';
        newDefaultBtn.classList.add('default');
        newDefaultItem.classList.add('priority');
        newDefaultItem.querySelector('.priority-indicator').style.display = 'inline';
        
        // 重新渲染以保持排序
        setTimeout(() => {
            renderVoicesList();
            // 自动保存默认语音设置
            autoSaveSettings("已设为默认语音");
        }, 300);
    }
    
    // 显示自动保存弹窗提示
    function showAutoSaveToast(message, isError = false) {
        // 检查是否已存在toast元素，如果不存在则创建
        let toastEl = document.getElementById('auto-save-toast');
        if (!toastEl) {
            toastEl = document.createElement('div');
            toastEl.id = 'auto-save-toast';
            toastEl.className = 'auto-save-toast';
            document.body.appendChild(toastEl);
        }

        // 设置消息内容和样式
        toastEl.textContent = message;
        toastEl.className = 'auto-save-toast' + (isError ? ' error' : ' success');
        
        // 显示toast
        setTimeout(() => {
            toastEl.classList.add('show');
        }, 10);
        
        // 自动隐藏
        setTimeout(() => {
            toastEl.classList.remove('show');
            setTimeout(() => {
                // 可选：从DOM中移除
                // toastEl.remove();
            }, 300);
        }, 2000);
    }

    // 自动保存设置并显示提示
    function autoSaveSettings(message) {
        // 保存收藏的语音
        try {
            localStorage.setItem('favoriteVoices', JSON.stringify(favoriteVoices));
            chrome.storage.local.set({ favoriteVoices });
        } catch (error) {
            console.error('保存收藏语音时出错:', error);
            showAutoSaveToast('保存设置失败: ' + error.message, true);
            return;
        }
        
        // 保存默认语音
        try {
            localStorage.setItem('defaultVoice', defaultVoice);
            chrome.storage.local.set({ currentVoice: defaultVoice });
            
            // 更新当前状态，确保默认语音被正确标记
            currentState.currentVoice = defaultVoice;
            
            // 更新主界面语音选择器
            voiceSelect.value = defaultVoice;
            
            // 触发change事件，确保值更改被检测到
            voiceSelect.dispatchEvent(new Event('change'));
            
            // 通知背景脚本更新默认语音
            chrome.runtime.sendMessage({
                action: 'setVoice',
                payload: defaultVoice
            });
            
            // 重新填充语音列表，确保界面更新
            populateVoiceList(availableVoices);
        } catch (error) {
            console.error('保存默认语音时出错:', error);
            showAutoSaveToast('保存设置失败: ' + error.message, true);
            return;
        }
        
        // 保存筛选状态
        try {
            localStorage.setItem('voiceFilters', JSON.stringify(currentFilters));
            chrome.storage.local.set({ voiceFilters: currentFilters });
        } catch (error) {
            console.error('保存筛选状态时出错:', error);
        }
        
        // 将设置同步到侧边栏
        chrome.runtime.sendMessage({
            action: 'updateVoiceOptions',
            defaultVoice: defaultVoice,
            favoriteVoices: favoriteVoices,
            voiceFilters: currentFilters
        });
        
        // 显示保存成功消息（使用新的toast提示）
        showAutoSaveToast(message);
    }
}

// 隐藏语音设置浮层
function hideVoiceSettingsOverlay() {
    const voiceSettingsOverlay = document.getElementById('voice-settings-overlay');
    const voiceSettingsContainer = document.getElementById('voice-settings-container');
    
    // 移除活动状态
    voiceSettingsOverlay.classList.remove('active');
    
    // 停止任何正在播放的语音试听
    chrome.tts.stop();
    
    // 延迟隐藏，等待过渡动画完成
    setTimeout(() => {
        voiceSettingsOverlay.style.display = 'none';
        
        // 清空容器内容，释放内存
        voiceSettingsContainer.innerHTML = '';
    }, 300);
}

// 原有的内嵌设置面板显示函数
function showOriginalSettingsOverlay() {
    settingsOverlay.style.display = 'flex';
    setTimeout(() => {
        settingsOverlay.classList.add('active');
    }, 10);
    
    // Sync values from main form to overlay
    overlaySpeedSlider.value = speedSlider.value;
    overlaySpeedValue.textContent = speedValueEl.textContent;
    overlayContinuousToggle.checked = continuousReadingToggle.checked;
    
    // 设置默认语音选择器的值
    defaultVoiceSelect.value = voiceSelect.value;
    
    // 渲染收藏的语音和可用语音列表
    renderFavoriteVoices();
    renderAvailableVoices();
}

function hideSettingsOverlay() {
    settingsOverlay.classList.remove('active');
    setTimeout(() => {
        settingsOverlay.style.display = 'none';
    }, 300);
}

// --- Event Listeners ---
settingsBtn.addEventListener('click', showSettingsOverlay);
closeSettingsBtn.addEventListener('click', () => {
    hideSettingsOverlay();
    // 不再需要向语音设置页面发送关闭消息
    // sendCloseMessageToVoiceSettings();
});

// 设置面板语速实时调整
overlaySpeedSlider.addEventListener('input', () => {
    const speed = parseFloat(overlaySpeedSlider.value).toFixed(1);
    overlaySpeedValue.textContent = `${speed}x`;

    // 同步主界面滑块
    speedSlider.value = speed;
    speedValueEl.textContent = `${speed}x`;

    // 实时调整语速（在播放状态下）
    if (currentState.readingState === 'reading') {
        // 使用防抖机制，避免过于频繁的调整
        clearTimeout(overlaySpeedSlider.adjustTimeout);
        overlaySpeedSlider.adjustTimeout = setTimeout(() => {
            const safeSpeed = Math.max(0.5, Math.min(5.0, parseFloat(speed)));
            console.log("设置面板实时调整语速到:", safeSpeed);

            try {
                chrome.runtime.sendMessage({ action: 'setSpeed', speed: safeSpeed }, (response) => {
                    if (chrome.runtime.lastError) {
                        console.error("设置面板实时调整语速时出错:", chrome.runtime.lastError);
                    } else {
                        console.log("设置面板实时语速调整成功:", response);
                    }
                });
            } catch (error) {
                console.error("设置面板发送实时语速调整消息时出错:", error);
            }
        }, 200); // 200ms防抖延迟
    }
});

overlaySpeedSlider.addEventListener('change', () => {
    const speed = parseFloat(overlaySpeedSlider.value);
    console.log("设置面板语速调整完成:", speed);

    // 限制语速范围，和背景脚本保持一致
    const safeSpeed = Math.max(0.5, Math.min(5.0, speed));

    // 同步更新主界面UI
    speedSlider.value = safeSpeed;
    speedValueEl.textContent = `${safeSpeed.toFixed(1)}x`;
    overlaySpeedValue.textContent = `${safeSpeed.toFixed(1)}x`;

    // 清除防抖计时器
    if (overlaySpeedSlider.adjustTimeout) {
        clearTimeout(overlaySpeedSlider.adjustTimeout);
        overlaySpeedSlider.adjustTimeout = null;
    }

    // 最终确认语速设置
    try {
        chrome.runtime.sendMessage({ action: 'setSpeed', speed: safeSpeed }, (response) => {
            if (chrome.runtime.lastError) {
                console.error("设置面板最终确认语速时出错:", chrome.runtime.lastError);
                showMessage(`调整语速时出错: ${chrome.runtime.lastError.message}`, true);
                setTimeout(() => requestStateUpdate(), 200);
            } else {
                console.log("设置面板语速最终确认成功:", response);
                
                // 检查是否中断了播放
                if (response && response.wasInterrupted === true) {
                    console.log("设置面板语速调整导致了播放中断，等待恢复...");
                    
                    // 显示临时状态
                    statusIndicatorEl.textContent = '正在恢复播放...';
                    statusIndicatorEl.className = 'status-indicator reading';
                    
                    // 等待状态恢复
                    setTimeout(() => {
                        requestStateUpdate();
                        
                        // 额外检查以确保状态一致
                        setTimeout(() => {
                            if (wasReading && 
                                currentState.readingState !== 'reading' && 
                                currentState.readingState !== 'navigating' &&
                                currentState.readingState !== 'changing_speed') {
                                console.log("检测到状态不一致，尝试恢复播放");
                                
                                // 尝试恢复播放
                                chrome.runtime.sendMessage({ action: 'startReading' }, () => {
                                    setTimeout(() => requestStateUpdate(), 200);
                                });
                            }
                        }, 500);
                    }, 300);
                } else {
                    // 无中断或非播放状态变化，简单更新UI
                    setTimeout(() => requestStateUpdate(), 200);
                }
            }
        });
    } catch (error) {
        console.error("设置面板发送语速调整消息时出错:", error);
        showMessage(`调整语速时出错: ${error.message}`, true);
        setTimeout(() => requestStateUpdate(), 200);
    }
});

overlayContinuousToggle.addEventListener('change', () => {
    const enabled = overlayContinuousToggle.checked;
    continuousReadingToggle.checked = enabled;
    sendMessageToBackground({ action: 'setContinuousReading', continuous: enabled });
});

// Close overlay when clicking outside content
settingsOverlay.addEventListener('click', (e) => {
    if (e.target === settingsOverlay) {
        hideSettingsOverlay();
        // 不再需要向语音设置页面发送关闭消息
        // sendCloseMessageToVoiceSettings();
    }
});

// 添加窗口关闭事件处理 - 只在真正关闭插件时停止播放
window.addEventListener('beforeunload', (event) => {
    console.log("侧边栏即将关闭，检查是否为插件关闭", "当前状态:", currentState.readingState);

    // 重置pendingAutoPlay标记，避免重新打开侧边栏时错误启动
    pendingAutoPlay = false;
    // 尝试清除session存储中的标记
    try {
        chrome.storage.session.remove('autoRefreshForPlay');
    } catch (e) {
        console.error("清除autoRefreshForPlay标记失败:", e);
    }

    // 检查是否为真正的插件关闭（而不是侧边栏隐藏）
    // 只有在插件被禁用、卸载或浏览器关闭时才停止播放
    const isPluginClosing = checkIfPluginClosing();

    if (isPluginClosing && (currentState.readingState === 'reading' || currentState.readingState === 'paused' ||
        currentState.readingState === 'navigating')) {
        try {
            // 高优先级发送停止命令 - 只在插件真正关闭时
            console.log("插件真正关闭，发送停止命令，状态:", currentState.readingState);
            
            // 尝试多种方法确保停止命令被发送
            
            // 1. 直接停止TTS
            try {
                chrome.tts.stop();
                // 再次检查是否真的停止了
                setTimeout(() => {
                    chrome.tts.isSpeaking((speaking) => {
                        if (speaking) {
                            console.log("第一次停止后仍在播放，再次尝试");
                            chrome.tts.stop();
                        }
                    });
                }, 100);
            } catch (e) {
                console.error("直接停止TTS失败:", e);
            }
            
            // 2. 使用同步XHR确保在关闭前发送
            try {
                const xhr = new XMLHttpRequest();
                xhr.open('POST', chrome.runtime.getURL('/_stop_reading_'), false); // 同步
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.send(JSON.stringify({
                    action: 'sidePanelClosing',
                    timestamp: Date.now(),
                    readingState: currentState.readingState
                }));
            } catch (e) {
                console.error("发送同步关闭消息失败:", e);
                // 如果XHR失败，再次尝试直接停止
                chrome.tts.stop();
            }
            
            // 3. 发送消息到后台
            try {
                chrome.runtime.sendMessage({
                    action: 'stopReading',
                    source: 'panel_closing'
                });

                // 同时发送专门的关闭消息
                chrome.runtime.sendMessage({
                    action: 'sidePanelClosing',
                    force: true,
                    timestamp: Date.now(),
                    readingState: currentState.readingState
                });
            } catch (e) {
                console.error("发送停止消息到后台失败:", e);
                // 如果消息发送失败，再次尝试直接停止
                chrome.tts.stop();
            }

            // 4. 强制停止所有可能的播放
            try {
                // 多次调用确保停止
                for (let i = 0; i < 3; i++) {
                    setTimeout(() => {
                        chrome.tts.stop();
                    }, i * 50);
                }
            } catch (e) {
                console.error("强制停止TTS失败:", e);
            }
        } catch (error) {
            console.error("停止播放失败:", error);
            // 最后的尝试
            chrome.tts.stop();
        }
    }
});

// 监听侧边栏可见性变化
document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'hidden') {
        console.log("侧边栏不可见，但不主动停止朗读");
    }
});

// 修改初始化函数
async function initializeSidePanel() {
    console.log("初始化侧边栏...");
    
    // 启用状态同步检查
    ensureStateSynchronization();
    
    // 添加可见性变化监听器，确保在侧边栏可见时状态最新
    document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') {
            console.log("侧边栏可见性恢复，重新请求状态");
            requestStateUpdate();
        }
    });
    
    // 添加窗口焦点事件监听器
    window.addEventListener('focus', () => {
        console.log("侧边栏获得焦点，检查状态");
        // 如果上次状态更新超过3秒，请求新状态
        if (Date.now() - lastStateUpdateTime > 3000) {
            requestStateUpdate();
        }
    });
    
    // 建立与后台的长连接
    try {
        const port = chrome.runtime.connect({name: `sidepanel_${Date.now()}`});
        console.log("与后台建立了长连接");
        
        // 定期发送心跳，确保连接保持活跃
        const heartbeatInterval = setInterval(() => {
            try {
                port.postMessage({type: 'heartbeat'});
            } catch (e) {
                console.error("心跳消息发送失败:", e);
                clearInterval(heartbeatInterval);
            }
        }, 15000); // 每15秒发送一次心跳
        
        // 监听连接断开
        port.onDisconnect.addListener(() => {
            console.log("与后台的连接已断开");
            clearInterval(heartbeatInterval);
            // 连接断开时尝试重连
            setTimeout(() => {
                try {
                    const newPort = chrome.runtime.connect({name: `sidepanel_${Date.now()}`});
                    console.log("重新建立与后台的连接");
                } catch (e) {
                    console.error("重连失败:", e);
                }
            }, 3000);
        });
    } catch (e) {
        console.error("建立与后台连接失败:", e);
    }
    
    // 设置一个全局的未处理异常捕获，确保任何错误都不会影响停止朗读操作
    window.onerror = function(message, source, lineno, colno, error) {
        console.error("未捕获的错误:", message, error);
        // 尝试停止朗读
        try {
            if (chrome && chrome.runtime) {
                chrome.runtime.sendMessage({ action: 'stopReading' });
            }
        } catch (e) {
            console.error("停止朗读失败:", e);
        }
        return false; // 让默认处理继续
    };
    
    // 加载收藏的语音
    loadFavoriteVoices();
    
    // 加载默认语音设置
    loadDefaultVoice();
    
    // 检查是否应保留自动播放标记
    if (typeof pendingAutoPlay !== 'undefined' && pendingAutoPlay) {
        console.log("检测到pendingAutoPlay标记，验证是否存在自动刷新标记...");
        
        chrome.storage.session.get(['autoRefreshForPlay'], (data) => {
            if (!data.autoRefreshForPlay) {
                console.log("没有检测到自动刷新标记，重置pendingAutoPlay");
                pendingAutoPlay = false;
            } else {
                console.log("确认存在自动刷新标记，保留pendingAutoPlay标记");
            }
        });
    }
    
    // 主动触发内容脚本解析当前页面
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tab && tab.id) {
            console.log("主动触发内容解析，当前标签页ID:", tab.id);
            // 执行内容脚本，确保页面内容被解析
            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                files: ["src/lib/Readability.js", "src/content/parser.js"]
            });
            console.log("内容脚本执行完成，等待解析结果...");
            // 给内容脚本一些时间来解析和发送数据
            setTimeout(() => {
                console.log("请求最新状态...");
                sendMessageToBackground({ action: 'getState' });
            }, 500);
        }
    } catch (error) {
        console.error("触发内容解析失败:", error);
    }
    
    // Request initial state from background
    sendMessageToBackground({ action: 'getState' });
    
    // 尝试两种方式获取语音列表
    await fetchVoicesList();

    // 设置默认时长为30分钟（1800秒）
    const defaultDurationSeconds = 30 * 60; // 30分钟 = 1800秒
    const defaultDurationPercentage = (defaultDurationSeconds / MAX_DURATION_SECONDS) * 100;
    
    // 直接设置正确的值，而不是手动计算百分比
    progressBar.style.width = `${defaultDurationPercentage}%`;
    timeBubbleEl.style.left = `${defaultDurationPercentage}%`;
    timeBubbleEl.textContent = formatTimeForBubble(defaultDurationSeconds);
    currentTimeEl.textContent = formatTime(defaultDurationSeconds);
    
    // 确保时间气泡在初始化时显示一下
    setTimeout(() => {
        progressBarContainer.classList.add('dragging');
        setTimeout(() => {
            progressBarContainer.classList.remove('dragging');
        }, 1500);
    }, 500);
    
    // 确保背景脚本也知道默认时长是30分钟
    sendMessageToBackground({ action: 'setReadingDuration', payload: defaultDurationSeconds });

    console.log("Default duration set to 30 minutes");

    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.action === 'stateUpdate') {
            console.log("DEBUG: Received state update from background:", JSON.stringify(message));
            
            // 特殊处理导航状态，确保在切换章节时UI状态保持一致
            if (message.state && message.state.readingState === 'navigating') {
                console.log("收到导航状态更新，保持播放状态UI");
                
                // 保存状态但不更新UI界面的播放/暂停状态
                currentState = { ...message.state };
                
                // 如果正在导航，确保播放按钮显示为"暂停"状态
                playPauseButton.classList.add('playing');
                playPauseButton.title = "暂停播放";
                statusIndicatorEl.textContent = translateState('navigating');
                statusIndicatorEl.className = 'status-indicator reading'; // 使用reading样式
                
                // 确保停止按钮可用
                stopBtn.disabled = false;
                
                // 禁用章节选择和连续阅读开关
                continuousReadingToggle.disabled = true;
                chapterInput.disabled = true;
                decrementChapterBtn.disabled = true;
                incrementChapterBtn.disabled = true;
                document.getElementById('setting-locked-hint').style.display = 'block';
                
                // 记录更新时间
                lastStateUpdateTime = Date.now();
            } else {
                // 正常更新UI
                updateUI(message.state);
            }
        }
        // 监听来自语音设置页面的更新消息
        else if (message.action === 'updateVoiceOptions') {
            console.log("收到语音设置更新:", message);
            // 更新默认语音
            if (message.defaultVoice !== undefined) {
                // 更新当前状态对象中的默认语音
                currentState.currentVoice = message.defaultVoice;
                console.log("更新当前默认语音:", currentState.currentVoice);
                
                // 向后台发送更新消息，确保后台状态同步
                sendMessageToBackground({ 
                    action: 'updateVoice', 
                    payload: message.defaultVoice 
                });
                
                console.log("默认语音已更新并同步到后台");
            }
            // 更新收藏的语音列表
            if (message.favoriteVoices) {
                favoriteVoices = message.favoriteVoices;
                // 保存到本地存储
                saveFavoriteVoices();
                // 重新渲染收藏语音列表
                renderFavoriteVoices();
                console.log("收藏语音已更新:", favoriteVoices);
            }
            
            // 这里直接调用重新填充语音列表函数，确保使用最新的数据
            console.log("开始重新填充语音下拉列表...");
            // 清空当前下拉选择框内容
            voiceSelect.innerHTML = '';
            // 重新填充
            populateVoiceList(availableVoices);
            console.log("语音下拉列表已重新填充完成，默认语音:", currentState.currentVoice);
            
            // 更新筛选标签状态
            if (message.voiceFilters) {
                console.log("收到筛选标签状态更新:", message.voiceFilters);
                // 保存到本地存储，以便下次打开语音设置页面时恢复
                try {
                    localStorage.setItem('voiceFilters', JSON.stringify(message.voiceFilters));
                    chrome.storage.local.set({ voiceFilters: message.voiceFilters });
                } catch (error) {
                    console.error("保存筛选标签状态时出错:", error);
                }
            }
        }
    });
}

// 获取语音列表的函数
async function fetchVoicesList() {
    console.log("开始获取语音列表...");
    
    // 首先尝试直接调用Chrome TTS API
    if (typeof chrome !== 'undefined' && chrome.tts && chrome.tts.getVoices) {
        console.log("尝试直接调用Chrome TTS API获取语音列表");
        try {
            const directPromise = new Promise(resolve => {
                chrome.tts.getVoices(voices => {
                    console.log("直接调用获取到语音数量:", voices?.length || 0);
                    if (voices && voices.length > 0) {
                        resolve(voices);
         } else {
                        resolve(null);
                    }
                });
            });
            
            // 设置5秒超时
            const timeoutPromise = new Promise(resolve => {
                setTimeout(() => resolve(null), 5000);
            });
            
            // 等待直接调用结果或超时
            const voices = await Promise.race([directPromise, timeoutPromise]);
            if (voices) {
                console.log("成功直接获取到语音列表");
                populateVoiceList(voices);
                
                // 确保在获取语音列表后加载默认语音
                console.log("确保默认语音被正确加载和应用");
                loadDefaultVoice();
                
                return true;
            } else {
                console.warn("直接获取语音列表失败或超时");
         }
    } catch (error) {
            console.error("直接调用TTS API出错:", error);
        }
    } else {
        console.warn("Chrome TTS API不可用，无法直接获取语音列表");
    }
    
    // 通过后台脚本获取语音列表
    console.log("尝试通过后台脚本获取语音列表");
    try {
        const bgPromise = new Promise(resolve => {
            sendMessageToBackground({ action: 'getVoices' }, (response) => {
                console.log("从背景脚本获取到响应:", response);
                if (response && response.voices && response.voices.length > 0) {
                    console.log("通过背景脚本获取到语音数量:", response.voices.length);
                    resolve(response.voices);
                } else {
                    console.warn("背景脚本未返回有效的语音列表");
                    resolve(null);
                }
            });
            
            // 处理通信错误
            setTimeout(() => {
                console.warn("与背景脚本通信超时");
                resolve(null);
            }, 5000);
        });
        
        const voices = await bgPromise;
        if (voices) {
            console.log("成功通过背景脚本获取语音列表");
            populateVoiceList(voices);
            
            // 确保在获取语音列表后加载默认语音
            console.log("确保默认语音被正确加载和应用");
            loadDefaultVoice();
            
            return true;
        } else {
            console.warn("通过背景脚本获取语音列表失败");
        }
    } catch (error) {
        console.error("通过背景脚本获取语音列表出错:", error);
    }
    
    // 如果都失败了，显示错误信息
    console.error("无法获取语音列表，所有方法都失败");
    availableVoicesList.innerHTML = '<div class="loading-voices">无法获取语音列表，可能是浏览器不支持TTS功能</div>';
    showMessage("无法获取可用语音，请检查浏览器TTS支持", true);
    return false;
}

// 获取管理语音收藏的按钮
const manageVoicesBtn = document.getElementById('manage-voices-btn');

// 添加管理语音收藏按钮的点击事件
manageVoicesBtn.addEventListener('click', () => {
    // 使用showSettingsOverlay函数打开语音设置窗口
    showSettingsOverlay();
});

// 获取快捷管理语音按钮
const quickManageVoicesBtn = document.getElementById('quick-manage-voices');

// 添加快捷管理语音按钮的点击事件（和manageVoicesBtn使用相同的功能）
quickManageVoicesBtn.addEventListener('click', (e) => {
    e.preventDefault();
    showSettingsOverlay();
});

// 添加章节卡片折叠/展开功能
if (chapterCardHeader && toggleChapterCardBtn && chapterDetails) {
    chapterCardHeader.addEventListener('click', toggleChapterDetails);
    toggleChapterCardBtn.addEventListener('click', (e) => {
        e.stopPropagation(); // 防止事件冒泡到header
        toggleChapterDetails();
    });
}

// 章节卡片折叠/展开函数
function toggleChapterDetails() {
    const isExpanded = chapterDetails.style.display === 'block';
    
    if (isExpanded) {
        chapterDetails.style.display = 'none';
        toggleChapterCardBtn.classList.remove('expanded');
    } else {
        chapterDetails.style.display = 'block';
        toggleChapterCardBtn.classList.add('expanded');
    }
}

// 定义向所有标签页发送消息的函数
function sendCloseMessageToVoiceSettings() {
    // 通过广播方式发送消息，确保所有语音设置页面都能收到
    chrome.runtime.sendMessage({ action: 'closeVoiceSettings' }, (response) => {
        if (chrome.runtime.lastError) {
            console.log('发送关闭消息时出错:', chrome.runtime.lastError);
        } else {
            console.log('已发送关闭语音设置页面的消息');
        }
    });
}

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeSidePanel);
} else {
    initializeSidePanel();
}

// 处理章节标题，遇到下划线就截断
function processChapterTitle(title) {
    if (!title) return title;
    
    // 查找第一个下划线的位置
    const underscoreIndex = title.indexOf('_');
    
    // 如果标题中包含下划线，则只保留下划线之前的内容
    if (underscoreIndex !== -1) {
        return title.substring(0, underscoreIndex).trim();
    }
    
    // 如果没有下划线，则返回原标题
    return title;
}

// --- 语音相关函数 ---
// 加载默认语音设置
function loadDefaultVoice() {
    console.log("加载默认语音设置...");
    try {
        // 优先从chrome.storage.local加载
        chrome.storage.local.get('currentVoice', (result) => {
            if (result.currentVoice) {
                // 更新当前状态对象中的默认语音
                currentState.currentVoice = result.currentVoice;
                console.log("从chrome.storage加载默认语音:", currentState.currentVoice);
                
                // 如果已经有语音列表，更新下拉框选择状态
                if (availableVoices && availableVoices.length > 0) {
                    // 设置选中值
                    voiceSelect.value = currentState.currentVoice;
                    defaultVoiceSelect.value = currentState.currentVoice;
                    
                    // 触发change事件，确保值更改被检测到
                    voiceSelect.dispatchEvent(new Event('change'));
                    
                    // 重新填充语音列表，确保默认语音有正确的样式标记
                    populateVoiceList(availableVoices);
                }
            } else {
                // 回退到localStorage
                const savedVoice = localStorage.getItem('defaultVoice');
                if (savedVoice) {
                    currentState.currentVoice = savedVoice;
                    console.log("从localStorage加载默认语音:", currentState.currentVoice);
                    
                    // 同步到chrome.storage.local
                    chrome.storage.local.set({ currentVoice: savedVoice });
                    
                    // 如果已经有语音列表，更新下拉框选择状态
                    if (availableVoices && availableVoices.length > 0) {
                        // 设置选中值
                        voiceSelect.value = currentState.currentVoice;
                        defaultVoiceSelect.value = currentState.currentVoice;
                        
                        // 触发change事件，确保值更改被检测到
                        voiceSelect.dispatchEvent(new Event('change'));
                        
                        // 重新填充语音列表，确保默认语音有正确的样式标记
                        populateVoiceList(availableVoices);
                    }
                }
            }
        });
    } catch (error) {
        console.error("加载默认语音设置失败:", error);
    }
}

// 确保状态同步的函数
function ensureStateSynchronization() {
    // 设置定期检查状态的定时器
    if (!stateCheckInterval) {
        console.log("启动状态同步检查计时器");
        stateCheckInterval = setInterval(() => {
            // 如果上次状态更新超过5秒，请求新状态
            const now = Date.now();
            if (now - lastStateUpdateTime > 5000) {
                console.log("状态检查: 已超过5秒未收到更新，主动请求状态");
                requestStateUpdate();
            }
            
            // 如果当前状态是播放或暂停，进行更频繁的检查
            if (currentState.readingState === 'reading' || currentState.readingState === 'paused' || 
                currentState.readingState === 'navigating') {
                console.log("播放中状态检查: 确认按钮状态与当前状态一致");
                // 确认UI状态与当前状态一致
                ensureUIMatchesState();
            }
        }, 5000); // 每5秒检查一次
    }
}

// 确保UI与状态一致
function ensureUIMatchesState() {
    // 检查播放/暂停按钮状态
    if (currentState.readingState === 'reading' || currentState.readingState === 'navigating') {
        // 应该显示为暂停图标
        if (!playPauseButton.classList.contains('playing')) {
            console.log("UI同步: 纠正播放按钮为暂停图标");
            playPauseButton.classList.add('playing');
            playPauseButton.title = "暂停播放";
        }
        
        // 确保停止按钮可用
        if (stopBtn.disabled) {
            console.log("UI同步: 启用停止按钮");
            stopBtn.disabled = false;
        }
        
        // 确保计时器在运行
        if (!playbackTimer) {
            console.log("UI同步: 启动计时器");
            startPlaybackTimer();
        }
    } else if (currentState.readingState === 'paused') {
        // 应该显示为播放图标
        if (playPauseButton.classList.contains('playing')) {
            console.log("UI同步: 纠正暂停按钮为播放图标");
            playPauseButton.classList.remove('playing');
            playPauseButton.title = "继续播放当前页";
        }
        
        // 确保停止按钮可用
        if (stopBtn.disabled) {
            console.log("UI同步: 启用停止按钮");
            stopBtn.disabled = false;
        }
        
        // 确保计时器已暂停
        if (playbackTimer) {
            console.log("UI同步: 暂停计时器");
            pausePlaybackTimer();
        }
    } else {
        // 空闲或停止状态
        if (playPauseButton.classList.contains('playing')) {
            console.log("UI同步: 纠正按钮为播放图标");
            playPauseButton.classList.remove('playing');
            playPauseButton.title = "点击直接播放当前页";
        }
        
        // 确保停止按钮禁用
        if (!stopBtn.disabled && (currentState.readingState === 'idle' || currentState.readingState === 'stopped')) {
            console.log("UI同步: 禁用停止按钮");
            stopBtn.disabled = true;
        }
        
        // 确保计时器已重置
        if (playbackTimer) {
            console.log("UI同步: 重置计时器");
            resetPlaybackTimer();
        }
    }
    
    // 更新状态指示器文本
    const expectedText = translateState(currentState.readingState);
    if (statusIndicatorEl.textContent !== expectedText) {
        console.log(`UI同步: 更新状态指示器，当前:${statusIndicatorEl.textContent} 预期:${expectedText}`);
        statusIndicatorEl.textContent = expectedText;
    }
    
    // 更新状态指示器类名
    const expectedClass = `status-indicator ${currentState.readingState === 'navigating' ? 'reading' : (currentState.readingState || 'idle')}`;
    if (!statusIndicatorEl.className.includes(expectedClass.split(' ')[1])) {
        console.log(`UI同步: 更新状态指示器类名，当前:${statusIndicatorEl.className} 预期:${expectedClass}`);
        statusIndicatorEl.className = expectedClass;
    }
}

// ===== URL输入和播放功能 =====

// URL验证函数
function isValidURL(string) {
    try {
        const url = new URL(string);
        return url.protocol === 'http:' || url.protocol === 'https:';
    } catch (_) {
        return false;
    }
}

// 显示URL验证消息
function showURLValidationMessage(message, isError = false) {
    if (!urlValidationMessage) return;

    urlValidationMessage.textContent = message;
    urlValidationMessage.className = `url-validation-message ${isError ? 'error' : 'success'}`;
    urlValidationMessage.style.display = 'block';

    // 3秒后自动隐藏
    setTimeout(() => {
        if (urlValidationMessage) {
            urlValidationMessage.style.display = 'none';
        }
    }, 3000);
}

// 播放指定URL的内容
async function playURL(url) {
    console.log("开始播放URL:", url);

    if (!isValidURL(url)) {
        showURLValidationMessage("请输入有效的网址", true);
        return;
    }

    try {
        // 禁用播放按钮，显示加载状态
        playUrlBtn.disabled = true;
        playUrlBtn.innerHTML = '<span class="play-url-icon">⏳</span>加载中...';

        // 向后台发送播放URL请求
        const response = await sendMessageToBackground({
            action: 'playURL',
            url: url
        });

        if (response && response.success) {
            showURLValidationMessage("开始播放网址内容", false);
            // 更新播放状态显示
            updatePlayingContentInfo(response.title || '未知标题', url);
        } else {
            showURLValidationMessage(response?.error || "播放失败，请检查网址是否有效", true);
        }
    } catch (error) {
        console.error("播放URL时出错:", error);
        showURLValidationMessage("播放失败，请稍后重试", true);
    } finally {
        // 恢复播放按钮状态
        playUrlBtn.disabled = false;
        playUrlBtn.innerHTML = '<span class="play-url-icon">▶</span>';
    }
}

// ===== 智能播放功能 =====

// 智能播放URL
async function handleSmartPlayURL(url) {
    if (!isValidURL(url)) {
        showURLValidationMessage("请输入有效的网址", true);
        return;
    }

    try {
        showMessage("正在加载网页内容...", false);
        statusIndicatorEl.textContent = '加载中';
        statusIndicatorEl.className = 'status-indicator loading';

        const response = await sendMessageToBackground({
            action: 'playURL',
            url: url
        });

        if (response && response.success) {
            showMessage("开始播放网址内容", false);
            // 历史记录由后台脚本自动添加
            // 更新播放状态显示
            updatePlayingContentInfo(response.title || '未知标题', url);
        } else {
            showMessage(response?.error || "播放失败，请检查网址是否有效", true);
            statusIndicatorEl.textContent = translateState('idle');
            statusIndicatorEl.className = 'status-indicator idle';
        }
    } catch (error) {
        console.error("播放URL时出错:", error);
        showMessage("播放失败，请稍后重试", true);
        statusIndicatorEl.textContent = translateState('idle');
        statusIndicatorEl.className = 'status-indicator idle';
    }
}

// 历史记录管理已移至后台脚本，在解析完成时自动添加

// 更新播放内容信息显示
function updatePlayingContentInfo(title, url) {
    if (!playingContentInfo || !playingTitle || !playingUrl) return;

    playingTitle.textContent = title || '未知标题';
    playingUrl.textContent = url || '';
    playingContentInfo.style.display = 'block';

    // 保存当前播放的URL，用于"打开页面"功能
    currentPlayingURL = url;
}

// 隐藏播放内容信息
function hidePlayingContentInfo() {
    if (playingContentInfo) {
        playingContentInfo.style.display = 'none';
    }
    currentPlayingURL = null;
}

// 打开正在播放的页面
async function openPlayingPage() {
    if (!currentPlayingURL) {
        showMessage("没有正在播放的内容", true);
        return;
    }

    try {
        await chrome.tabs.create({ url: currentPlayingURL });
        showMessage("已在新标签页中打开播放页面", false);
    } catch (error) {
        console.error("打开页面时出错:", error);
        showMessage("打开页面失败", true);
    }
}

// 当前播放的URL
let currentPlayingURL = null;

// 播放历史记录管理
let playbackHistory = [];
const MAX_HISTORY_ITEMS = 20; // 最大历史记录数量

// 加载播放历史记录
async function loadPlaybackHistory() {
    try {
        const result = await chrome.storage.local.get(['playbackHistory']);
        playbackHistory = result.playbackHistory || [];
        console.log("加载播放历史记录:", playbackHistory.length, "条");
        renderPlaybackHistory();
    } catch (error) {
        console.error("加载播放历史失败:", error);
        playbackHistory = [];
    }
}

// 历史记录现在由后台脚本统一管理，侧边栏只负责显示

// 从播放历史中删除项目
async function removeFromPlaybackHistory(index) {
    if (index >= 0 && index < playbackHistory.length) {
        playbackHistory.splice(index, 1);
        try {
            await chrome.storage.local.set({ playbackHistory: playbackHistory });
            renderPlaybackHistory();
        } catch (error) {
            console.error("删除历史记录失败:", error);
        }
    }
}

// 清空播放历史
async function clearPlaybackHistory() {
    playbackHistory = [];
    try {
        await chrome.storage.local.set({ playbackHistory: playbackHistory });
        renderPlaybackHistory();
    } catch (error) {
        console.error("清空历史记录失败:", error);
    }
}

// 渲染播放历史记录
function renderPlaybackHistory() {
    if (!historyList) return;

    if (playbackHistory.length === 0) {
        historyList.innerHTML = '<div class="empty-history">暂无播放历史</div>';
        return;
    }

    historyList.innerHTML = playbackHistory.map((item, index) => {
        const date = new Date(item.timestamp);
        const timeStr = date.toLocaleString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });

        // 显示信息优化：优先显示小说名称和章节
        let displayTitle = item.title;
        let novelInfo = '';

        if (item.novelName && item.chapter) {
            // 如果有小说名称和章节信息，优化显示
            displayTitle = `${item.novelName} - ${item.chapter}`;
            novelInfo = `<div class="history-novel-info">📚 ${item.novelName}</div>`;
        } else if (item.novelName) {
            // 只有小说名称
            displayTitle = item.novelName;
            novelInfo = `<div class="history-novel-info">📚 ${item.novelName}</div>`;
        }

        const chapterInfo = item.chapter ? `<div class="history-chapter-info">章节：${item.chapter}</div>` : '';

        // 来源标识
        const sourceIcon = item.source === 'url_input' ? '🔗' : '📄';
        const sourceText = item.source === 'url_input' ? 'URL输入' : '当前页面';

        // 站点信息
        const siteInfo = item.siteName ? `<span class="history-site-name">${item.siteName}</span>` : '';

        return `
            <div class="history-item" onclick="playHistoryItem(${index})" title="点击播放此内容">
                <div class="history-item-header">
                    <div class="history-title-text" title="${item.title}">${displayTitle}</div>
                    <button class="history-delete-btn" onclick="event.stopPropagation(); removeFromPlaybackHistory(${index})" title="删除此记录">
                        ✕
                    </button>
                </div>
                ${novelInfo}
                ${chapterInfo}
                <div class="history-meta">
                    <span class="history-source" title="${sourceText}">${sourceIcon}</span>
                    ${siteInfo}
                    <span class="history-time">${timeStr}</span>
                </div>
                <div class="history-url" title="${item.url}">${item.url}</div>
            </div>
        `;
    }).join('');
}

// 播放历史记录中的项目
function playHistoryItem(index) {
    if (index >= 0 && index < playbackHistory.length) {
        const item = playbackHistory[index];
        console.log("播放历史项目:", item);

        // 将URL填入输入框
        if (smartUrlInput) {
            smartUrlInput.value = item.url;
        }

        // 触发播放
        handleSmartPlayURL(item.url);
    }
}

// ===== 事件监听器设置 =====

// 智能URL输入框事件
if (smartUrlInput) {
    // 实时验证URL
    smartUrlInput.addEventListener('input', function() {
        const url = this.value.trim();
        if (url && !isValidURL(url)) {
            this.style.borderColor = '#e74c3c';
            showURLValidationMessage("请输入有效的网址格式", true);
        } else {
            this.style.borderColor = '';
            if (urlValidationMessage) {
                urlValidationMessage.style.display = 'none';
            }
        }
    });

    // 回车键触发播放按钮
    smartUrlInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            // 触发主播放按钮的点击事件
            if (playPauseButton && !playPauseButton.disabled) {
                playPauseButton.click();
            }
        }
    });
}

// 播放历史记录事件
if (historyHeader && historyToggleBtn && historyContent) {
    // 历史记录折叠/展开
    historyHeader.addEventListener('click', function() {
        const isExpanded = historyContent.style.display !== 'none';

        if (isExpanded) {
            historyContent.style.display = 'none';
            historyToggleBtn.classList.remove('expanded');
        } else {
            historyContent.style.display = 'block';
            historyToggleBtn.classList.add('expanded');
        }
    });
}

// 清空历史记录按钮
if (clearHistoryBtn) {
    clearHistoryBtn.addEventListener('click', function() {
        if (confirm('确定要清空所有播放历史记录吗？')) {
            clearPlaybackHistory();
        }
    });
}

// 打开播放页面按钮事件
if (openPlayingPageBtn) {
    openPlayingPageBtn.addEventListener('click', openPlayingPage);
}

// ===== 初始化功能 =====

// 初始化播放历史记录
function initializePlaybackHistory() {
    loadPlaybackHistory();
}

// 获取并显示当前页面标题（如果需要显示的话）
async function updateCurrentPageInfo() {
    // 这个函数现在主要用于调试，因为UI已经简化
    try {
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tabs && tabs.length > 0) {
            console.log("当前页面:", tabs[0].title, tabs[0].url);
        }
    } catch (error) {
        console.error("获取当前页面信息时出错:", error);
    }
}

// 监听标签页变化，更新当前页面信息
if (typeof chrome !== 'undefined' && chrome.tabs) {
    chrome.tabs.onActivated.addListener(updateCurrentPageInfo);
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
        if (changeInfo.title) {
            updateCurrentPageInfo();
        }
    });
}

// 监听后台脚本的历史记录更新通知
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'historyUpdated') {
        console.log("收到历史记录更新通知，重新加载历史记录");
        loadPlaybackHistory();
        sendResponse({ status: "history_reloaded" });
    }
});

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePlaybackHistory();
    updateCurrentPageInfo();
});

// 如果DOM已经加载完成，立即执行
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        initializePlaybackHistory();
        updateCurrentPageInfo();
    });
} else {
    initializePlaybackHistory();
    updateCurrentPageInfo();
}

// 请求状态更新
function requestStateUpdate() {
    try {
        console.log("请求最新状态更新");
        sendMessageToBackground({ action: 'getState' });
    } catch (e) {
        console.error("请求状态更新失败:", e);
    }
}



// 检查是否为插件真正关闭（而不是侧边栏隐藏）
function checkIfPluginClosing() {
    try {
        // 检查是否为浏览器关闭或插件被禁用
        // 这是一个简化的检查，实际情况下很难准确判断

        // 如果能正常访问chrome API，说明插件还在运行
        if (chrome && chrome.runtime && chrome.runtime.id) {
            // 插件仍在运行，可能只是侧边栏隐藏
            console.log("插件仍在运行，可能只是侧边栏隐藏，不停止播放");
            return false;
        }
    } catch (e) {
        // 如果访问chrome API出错，可能是插件被禁用
        console.log("无法访问chrome API，可能是插件被禁用，停止播放");
        return true;
    }

    // 默认不停止播放，除非确定是插件关闭
    return false;
}

// 注意：只有用户主动点击停止按钮或关闭插件才能停止播放
// 其他情况（切换标签页、侧边栏不可见等）都应该继续播放
