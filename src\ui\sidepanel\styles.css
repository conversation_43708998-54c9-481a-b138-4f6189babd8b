/* 神灯AI·灵阅 - 统一样式文件 */

/* === 字体层次定义 === */
/*
层次1: 软件标题/主标题 - 16px
层次2: 区域标题/卡片标题 - 14px
层次3: 功能标签/设置项 - 12px
层次4: 普通文本/内容 - 11px
层次5: 辅助信息/提示 - 10px
层次6: 次要信息/状态 - 9px
*/

/* 基础布局修复 */
body {
    font-size: 11px !important; /* 层次4: 普通文本基准 */
    line-height: 1.3 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    height: 100vh !important;
    margin: 0 !important;
    padding: 0 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #10121A 0%, #181528 50%, #121A18 100%);
    color: #F0F0F5;
}

.container {
    padding: 10px !important;
    min-height: 100vh !important;
    box-sizing: border-box !important;
}

/* 卡片样式紧凑化 */
.card {
    margin: 6px 0 !important;
    padding: 8px !important;
    border-radius: 6px !important;
    background: rgba(35, 32, 55, 0.6) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* === 字体层次应用 === */

/* 层次1: 软件主标题 */
.logo-text, .app-title {
    font-size: 16px !important; /* 层次1 */
    font-weight: 600 !important;
    line-height: 1.2 !important;
}

/* 层次2: 区域标题 */
h1, h2, .section-title, .settings-header h2 {
    font-size: 14px !important; /* 层次2 */
    font-weight: 600 !important;
    margin: 8px 0 6px 0 !important;
    line-height: 1.3 !important;
}

/* 层次3: 功能标签和设置项 */
h3, .section-header h3, label, .setting-item label, .chapters-label {
    font-size: 12px !important; /* 层次3 */
    font-weight: 500 !important;
    margin: 4px 0 !important;
    line-height: 1.3 !important;
}

/* 层次4: 普通文本内容 */
p, .normal-text, button, input, select, textarea {
    font-size: 11px !important; /* 层次4 */
    line-height: 1.3 !important;
    margin: 3px 0 !important;
}

/* 层次5: 辅助信息 */
.hint-text, .duration-hint, .setting-locked-hint, .help-text {
    font-size: 10px !important; /* 层次5 */
    color: #A09CB0 !important;
    line-height: 1.2 !important;
}

/* 层次6: 次要信息 */
.time-display, .status-text, .url-text, small {
    font-size: 9px !important; /* 层次6 */
    color: #A09CB0 !important;
    line-height: 1.2 !important;
}

/* === 按钮字体层次 === */

/* 普通按钮 - 层次4 */
button {
    padding: 6px 12px !important;
    font-size: 11px !important; /* 层次4 */
    font-weight: 500 !important;
    border-radius: 6px !important;
    min-height: 28px !important;
    background: rgba(225, 173, 91, 0.1);
    border: 1px solid rgba(225, 173, 91, 0.3);
    color: #F0F0F5;
    cursor: pointer;
    transition: all 0.3s ease;
}

button:hover {
    background: rgba(225, 173, 91, 0.2);
    border-color: rgba(225, 173, 91, 0.5);
}

/* 控制按钮 - 层次4 */
.control-btn {
    min-width: 28px !important;
    height: 28px !important;
    font-size: 11px !important; /* 层次4 */
    font-weight: 500 !important;
}

/* 主要播放按钮 - 层次3 */
.main-btn {
    min-width: 36px !important;
    height: 36px !important;
    font-size: 12px !important; /* 层次3 - 重要功能 */
    font-weight: 600 !important;
}

/* 小按钮 - 层次5 */
.small-btn, .chapter-btn {
    font-size: 10px !important; /* 层次5 */
    padding: 4px 8px !important;
    min-height: 24px !important;
}

/* 设置按钮 - 层次5 */
.settings-btn {
    font-size: 10px !important; /* 层次5 */
}

/* 控制按钮容器紧凑化 */
.control-buttons-container {
    gap: 6px !important;
    margin: 8px 0 !important;
}

/* === 状态和输入控件字体层次 === */

/* 状态指示器 - 层次3 (重要状态) */
.status-indicator {
    font-size: 12px !important; /* 层次3 */
    font-weight: 600 !important;
    padding: 6px 10px !important;
    margin: 6px 0 !important;
    border-radius: 6px !important;
}

/* 输入框 - 层次4 (用户输入) */
input, select, textarea {
    padding: 6px 8px !important;
    font-size: 11px !important; /* 层次4 */
    border-radius: 4px !important;
    font-weight: 400 !important;
    background: rgba(35, 32, 55, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.12);
    color: #F0F0F5;
}

/* 重要输入框 - 层次3 */
.smart-url-input, .chapter-input {
    font-size: 12px !important; /* 层次3 */
    font-weight: 500 !important;
    padding: 8px 10px !important;
}

/* === 播放内容和播放历史统一字体大小 === */

/* 播放内容信息 - 与播放历史保持一致 */
.playing-content-info {
    font-size: 10px !important; /* 统一使用层次5 */
    padding: 6px !important;
    margin: 6px 0 !important;
    background: rgba(35, 32, 55, 0.4);
    border-radius: 4px !important;
    border: 1px solid rgba(255, 255, 255, 0.08);
}

.playing-content-info .playing-label {
    font-size: 10px !important; /* 与历史记录标题一致 */
    font-weight: 500 !important;
    color: #A09CB0;
}

.playing-content-info .playing-title {
    font-size: 10px !important; /* 与历史记录标题一致 */
    font-weight: 600 !important;
    color: #F0F0F5;
    margin: 2px 0 !important;
}

.playing-content-info .playing-url {
    font-size: 9px !important; /* 与历史记录URL一致 */
    color: #A09CB0;
    opacity: 0.8;
}

.playing-content-info .open-page-btn {
    font-size: 9px !important;
    padding: 2px 4px !important;
    background: #E1AD5B;
    color: #10121A;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

/* 播放历史记录 - 统一字体大小 */
.playback-history-section {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 6px !important;
    margin-top: 6px !important;
}

.history-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 3px 6px !important;
    cursor: pointer;
}

.history-title {
    color: #F0F0F5;
    font-size: 12px !important; /* 层次3 - 功能标签 */
    font-weight: 600;
}

.history-toggle-btn {
    background: none;
    border: none;
    color: #A09CB0;
    cursor: pointer;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    font-size: 8px !important;
    transition: all 0.3s ease;
}

.history-item {
    background: rgba(35, 32, 55, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 4px !important;
    padding: 6px !important;
    margin-bottom: 4px !important;
    cursor: pointer;
    transition: all 0.3s ease;
}

.history-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.history-item-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 3px !important;
}

.history-title-text {
    color: #F0F0F5;
    font-size: 10px !important; /* 与播放内容标题一致 */
    font-weight: 600;
    flex: 1;
    margin-right: 4px !important;
    line-height: 1.2 !important;
}

.history-delete-btn {
    background: none;
    border: none;
    color: #A09CB0;
    cursor: pointer;
    padding: 1px 3px !important;
    border-radius: 2px !important;
    font-size: 8px !important;
    transition: all 0.3s ease;
    opacity: 0.7;
}

.history-delete-btn:hover {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    opacity: 1;
}

.history-novel-info {
    font-size: 10px !important;
    color: #4a90e2 !important;
    margin: 2px 0 !important;
    font-weight: 500 !important;
}

.history-chapter-info {
    font-size: 9px !important;
    color: #A09CB0;
    margin-bottom: 2px !important;
    font-style: italic;
}

.history-meta {
    display: flex;
    align-items: center;
    gap: 4px !important;
    margin-bottom: 3px !important;
    font-size: 8px !important;
}

.history-url {
    color: #A09CB0;
    font-size: 9px !important; /* 与播放内容URL一致 */
    opacity: 0.7;
    word-break: break-all;
    line-height: 1.1 !important;
    margin-top: 2px !important;
}

/* 清空历史按钮 */
.clear-history-btn {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    padding: 4px 8px !important;
    border-radius: 3px !important;
    cursor: pointer;
    font-size: 8px !important;
    font-weight: 600;
    transition: all 0.3s ease;
}

.clear-history-btn:hover {
    background: rgba(231, 76, 60, 0.2);
}

/* 空历史提示 */
.empty-history {
    text-align: center;
    color: #A09CB0;
    font-size: 9px !important;
    font-style: italic;
    padding: 8px !important;
    background: rgba(35, 32, 55, 0.3);
    border-radius: 3px !important;
    border: 1px dashed rgba(255, 255, 255, 0.1);
}

/* === 播放控制相关样式 === */

/* 播放计时器 */
.playback-timer {
    font-size: 12px !important; /* 层次3 */
    font-weight: 600 !important;
    padding: 6px 10px !important;
    margin: 6px 0 !important;
    background: rgba(225, 173, 91, 0.1) !important;
    border-radius: 6px !important;
    color: #E1AD5B;
    text-align: center;
    font-family: 'Courier New', monospace;
}

/* 进度条相关 */
.progress-section {
    margin: 8px 0 !important;
}

.progress-bar-container {
    position: relative;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    cursor: pointer;
    margin: 8px 0;
}

#progress-bar {
    height: 100%;
    background: #E1AD5B;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.time-bubble {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: #E1AD5B;
    color: #10121A;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.progress-bar-container:hover .time-bubble,
.progress-bar-container.dragging .time-bubble {
    opacity: 1;
}

.duration-hint {
    font-size: 12px !important;
    color: #A09CB0 !important;
    text-align: center !important;
    margin: 4px 0 !important;
    font-weight: 500 !important;
}

.time-display {
    display: flex !important;
    justify-content: space-between !important;
    font-size: 11px !important;
    color: #F0F0F5 !important;
    margin-top: 6px !important;
    font-weight: 500 !important;
}

/* 剩余时间显示 */
.remaining-time-display {
    background: rgba(225, 173, 91, 0.1);
    padding: 4px 8px;
    margin: 6px 0;
    border-radius: 4px;
    border: 1px solid rgba(225, 173, 91, 0.2);
}

.remaining-label {
    font-size: 10px !important; /* 层次5 - 辅助信息 */
    color: #A09CB0 !important;
    margin-right: 6px !important;
}

.remaining-time {
    font-size: 11px !important; /* 层次4 - 重要信息 */
    color: #E1AD5B !important;
    font-weight: 600 !important;
    font-family: 'Courier New', monospace !important;
}

/* === 滑块样式 === */
.slider-container {
    margin: 6px 0 !important;
}

.slider-label {
    font-size: 11px !important;
    margin-bottom: 3px !important;
    color: #F0F0F5;
    font-weight: 500;
}

.slider {
    width: 100%;
    height: 4px !important;
    border-radius: 2px;
    background: rgba(255, 255, 255, 0.1);
    outline: none;
    cursor: pointer;
}

.slider::-webkit-slider-thumb {
    appearance: none;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #E1AD5B;
    cursor: pointer;
}

.slider-value {
    font-size: 10px !important;
    padding: 2px 6px !important;
    background: rgba(225, 173, 91, 0.1);
    border-radius: 3px;
    color: #E1AD5B;
    font-weight: 600;
    margin-left: 8px;
}

/* === 连续播放设置 === */
.continuous-reading-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 8px 0;
}

.continuous-reading-row label {
    font-size: 12px !important; /* 层次3 - 功能标签 */
    font-weight: 500 !important;
    color: #F0F0F5;
}

.continuous-chapters-row {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 6px 0;
}

.chapters-label {
    font-size: 12px !important; /* 层次3 - 功能标签 */
    font-weight: 500 !important;
    color: #F0F0F5;
}

.chapter-input {
    font-size: 12px !important; /* 层次3 - 重要输入 */
    font-weight: 600 !important;
    text-align: center !important;
    min-height: 28px !important;
    width: 60px;
    background: rgba(35, 32, 55, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.12);
    color: #F0F0F5;
    border-radius: 4px;
}

.chapter-btn {
    font-size: 11px !important; /* 层次4 */
    font-weight: 600 !important;
    min-width: 28px !important;
    min-height: 28px !important;
    background: rgba(225, 173, 91, 0.1);
    border: 1px solid rgba(225, 173, 91, 0.3);
    color: #E1AD5B;
    border-radius: 4px;
    cursor: pointer;
}

.chapter-btn:hover {
    background: rgba(225, 173, 91, 0.2);
}

/* 设置锁定提示 */
.setting-locked-hint {
    font-size: 10px !important; /* 层次5 - 辅助信息 */
    color: #A09CB0 !important;
    font-style: italic !important;
    margin-top: 4px;
}

/* === 开关样式 === */
.toggle-switch {
    position: relative;
    width: 40px;
    height: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.toggle-switch.active {
    background: #E1AD5B;
}

.toggle-switch::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.toggle-switch.active::after {
    transform: translateX(20px);
}

/* === 章节信息卡片 === */
.chapter-info-card {
    margin-top: 8px;
}

.card-header-collapsible {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    padding: 6px;
}

.toggle-chapter-card {
    background: none;
    border: none;
    color: #A09CB0;
    font-size: 10px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.chapter-details {
    padding: 6px;
    font-size: 10px;
    color: #A09CB0;
    line-height: 1.4;
}

.chapter-details p {
    margin: 4px 0;
}

.chapter-details strong {
    color: #F0F0F5;
    font-weight: 600;
}

/* === 消息提示 === */
.message-area {
    font-size: 10px !important;
    padding: 6px 8px;
    margin: 6px 0;
    border-radius: 4px;
    text-align: center;
    transition: all 0.3s ease;
}

.message-area.success {
    background: rgba(56, 161, 105, 0.1);
    border: 1px solid rgba(56, 161, 105, 0.3);
    color: #68d391;
}

.message-area.error {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.3);
    color: #fc8181;
}

/* === 响应式调整 === */
@media (max-width: 300px) {
    .container {
        padding: 6px !important;
    }

    .control-btn {
        min-width: 24px !important;
        height: 24px !important;
        font-size: 10px !important;
    }

    .main-btn {
        min-width: 32px !important;
        height: 32px !important;
        font-size: 12px !important;
    }
}

/* === 滚动条样式 === */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: rgba(225, 173, 91, 0.3);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(225, 173, 91, 0.5);
}

/* === 确保容器可滚动 === */
.container {
    overflow-y: auto !important;
}

/* === 软件标题特别强调 === */
.logo-text {
    font-size: 16px !important; /* 层次1 - 最大 */
    font-weight: 700 !important; /* 加粗 */
    color: #F0F0F5 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3) !important; /* 添加阴影 */
}

.slogan-symbol {
    font-size: 14px !important;
    opacity: 0.8 !important;
}

/* 确保字体层次不被覆盖 */
* {
    box-sizing: border-box;
}

/* 隐藏元素 */
.hidden {
    display: none !important;
}

/* 显示元素 */
.visible {
    display: block !important;
}
