/* 神灯AI·灵阅 V0.36 - 统一样式文件 */

/* === 字体大小层次系统 === */
/* 层次1: 软件主标题 - 16px */
/* 层次2: 区域标题 - 14px */
/* 层次3: 功能标签 - 12px */
/* 层次4: 普通文本 - 11px */
/* 层次5: 辅助信息 - 10px */
/* 层次6: 次要信息 - 9px */
/* 层次7: 最小信息 - 8px */

/* === 基础样式重置 === */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 11px !important; /* 层次4: 普通文本基准 */
    line-height: 1.3 !important;
    overflow-y: auto !important;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #F0F0F5;
    width: 100%;
    min-height: 100vh;
    padding: 0;
    margin: 0;
}

.container {
    width: 100%;
    max-width: 100%;
    padding: 8px;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* === 字体大小层次定义 === */

/* 层次1: 软件主标题 */
.logo-text, .app-title {
    font-size: 16px !important; /* 层次1 */
    font-weight: 700 !important;
    color: #F0F0F5 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3) !important;
    line-height: 1.2 !important;
}

/* 层次2: 区域标题 */
h1, h2, .section-title, .settings-header h2, .section-header h3 {
    font-size: 14px !important; /* 层次2 */
    font-weight: 600 !important;
    margin: 8px 0 6px 0 !important;
    line-height: 1.3 !important;
    color: #F0F0F5;
}

/* 层次3: 功能标签和设置项 */
h3, label, .setting-item label, .chapters-label, .history-title {
    font-size: 12px !important; /* 层次3 */
    font-weight: 600 !important;
    margin: 4px 0 !important;
    line-height: 1.3 !important;
    color: #F0F0F5;
}

/* 层次4: 普通文本内容 */
p, .normal-text, button, input, select, textarea {
    font-size: 11px !important; /* 层次4 */
    line-height: 1.3 !important;
    margin: 3px 0 !important;
}

/* 层次5: 辅助信息 */
.hint-text, .duration-hint, .setting-locked-hint, .help-text {
    font-size: 10px !important; /* 层次5 */
    color: #A09CB0 !important;
    line-height: 1.2 !important;
}

/* 层次6: 次要信息 */
.time-display, .status-text, .url-text, small {
    font-size: 9px !important; /* 层次6 */
    color: #A09CB0 !important;
    line-height: 1.2 !important;
}

/* === 卡片样式 === */
.card {
    background: rgba(35, 32, 55, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    backdrop-filter: blur(10px);
}

/* === 头部样式 === */
.panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 12px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.app-icon {
    width: 24px;
    height: 24px;
}

.slogan-symbol {
    font-size: 14px !important;
    opacity: 0.8 !important;
}

/* === 按钮样式 === */
button {
    background: rgba(65, 105, 225, 0.8);
    color: #F0F0F5;
    border: none;
    border-radius: 6px;
    padding: 6px 12px !important;
    font-size: 11px !important; /* 层次4 */
    font-weight: 500 !important;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 28px !important;
}

button:hover {
    background: rgba(65, 105, 225, 1);
    transform: translateY(-1px);
}

/* 小按钮 */
.small-btn, .chapter-btn {
    font-size: 10px !important; /* 层次5 */
    padding: 4px 8px !important;
    min-height: 24px !important;
    min-width: 28px !important;
}

/* 主要按钮 */
.main-btn {
    min-width: 36px !important;
    height: 36px !important;
    font-size: 12px !important; /* 层次3 - 重要功能 */
    font-weight: 600 !important;
}

/* 设置按钮 */
.settings-btn {
    font-size: 10px !important; /* 层次5 */
    background: rgba(225, 173, 91, 0.8);
}

.settings-btn:hover {
    background: rgba(225, 173, 91, 1);
}

/* === 输入框样式 === */
input, select, textarea {
    background: rgba(35, 32, 55, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 4px;
    padding: 6px 8px !important;
    font-size: 11px !important; /* 层次4 */
    color: #F0F0F5;
    font-weight: 400 !important;
}

/* 重要输入框 */
.smart-url-input, .chapter-input {
    font-size: 12px !important; /* 层次3 */
    font-weight: 500 !important;
    padding: 8px 10px !important;
}

/* === 状态指示器 === */
.status-indicator {
    font-size: 12px !important; /* 层次3 */
    font-weight: 600 !important;
    padding: 6px 10px !important;
    margin: 6px 0 !important;
    border-radius: 6px;
    text-align: center;
    background: rgba(35, 32, 55, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-indicator.idle {
    color: #A09CB0;
}

.status-indicator.reading {
    color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
}

.status-indicator.paused {
    color: #FF9800;
    background: rgba(255, 152, 0, 0.1);
}

.status-indicator.loading {
    color: #2196F3;
    background: rgba(33, 150, 243, 0.1);
}

/* === 播放控制 === */
.playback-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.control-buttons-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.playback-timer {
    font-size: 12px !important; /* 层次3 */
    font-weight: 600 !important;
    padding: 6px 10px !important;
    margin: 6px 0 !important;
    background: rgba(35, 32, 55, 0.6);
    border-radius: 6px;
    color: #E1AD5B;
    font-family: 'Courier New', monospace;
}

/* === 进度条 === */
.progress-section {
    width: 100%;
    margin: 8px 0;
}

.progress-bar-container {
    position: relative;
    width: 100%;
    height: 20px;
    background: rgba(35, 32, 55, 0.6);
    border-radius: 10px;
    cursor: pointer;
    margin-bottom: 4px;
}

.time-display {
    display: flex !important;
    justify-content: space-between !important;
    font-size: 11px !important;
    color: #F0F0F5 !important;
    margin-top: 6px !important;
    font-weight: 500 !important;
}

.duration-hint {
    font-size: 12px !important;
    color: #A09CB0 !important;
    text-align: center !important;
    margin: 4px 0 !important;
    font-weight: 500 !important;
}

/* === URL输入和播放历史 === */
.url-input-section {
    width: 100%;
}

.smart-url-input {
    width: 100%;
    margin-bottom: 8px;
    border: 1px solid rgba(255, 255, 255, 0.12) !important;
    border-radius: 6px !important;
    font-size: 12px !important; /* 层次3 - 重要输入 */
    font-weight: 500 !important;
    background: rgba(35, 32, 55, 0.6);
    color: #F0F0F5;
}

.smart-url-input::placeholder {
    color: #A09CB0;
    opacity: 0.8;
    font-size: 11px !important;
}

/* === 播放历史样式 === */
.playback-history-section {
    margin-top: 12px;
}

.history-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    padding: 6px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.history-title {
    color: #F0F0F5;
    font-size: 12px !important; /* 层次3 - 功能标签 */
    font-weight: 600;
}

.history-toggle-btn {
    background: none;
    border: none;
    color: #A09CB0;
    cursor: pointer;
    padding: 2px 4px;
    font-size: 10px !important;
}

.toggle-icon {
    font-size: 8px !important;
    transition: transform 0.3s ease;
}

.history-content {
    margin-top: 8px;
}

.empty-history {
    text-align: center;
    color: #A09CB0;
    font-size: 10px !important;
    font-style: italic;
    padding: 12px !important;
    background: rgba(35, 32, 55, 0.3);
    border-radius: 6px;
}

/* === 历史记录项样式 === */
.history-item {
    background: rgba(35, 32, 55, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 6px;
    padding: 8px;
    margin-bottom: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.history-item:hover {
    background: rgba(35, 32, 55, 0.6);
    border-color: rgba(255, 255, 255, 0.15);
}

.history-item-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 4px;
}

.history-title-text {
    color: #F0F0F5;
    font-size: 11px !important; /* 层次4 - 与播放内容标题一致 */
    font-weight: 600;
    flex: 1;
    margin-right: 4px !important;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.history-delete-btn {
    background: rgba(231, 76, 60, 0.8);
    color: white;
    border: none;
    padding: 1px 3px !important;
    border-radius: 2px !important;
    font-size: 8px !important;
    transition: all 0.3s ease;
    opacity: 0.7;
    cursor: pointer;
    min-height: auto !important;
    min-width: auto !important;
}

.history-delete-btn:hover {
    background: rgba(231, 76, 60, 1);
    opacity: 1;
}

.history-novel-info {
    font-size: 10px !important;
    color: #4a90e2 !important;
    margin: 2px 0 !important;
    font-weight: 500 !important;
}

.history-chapter-info {
    color: #A09CB0;
    font-size: 9px !important;
    margin-bottom: 2px !important;
    font-style: italic;
}

.history-meta {
    display: flex;
    align-items: center;
    gap: 4px !important;
    margin-bottom: 3px !important;
    font-size: 8px !important;
}

.history-source {
    font-size: 8px !important;
    opacity: 0.8;
}

.history-site-name {
    color: #E1AD5B;
    font-weight: 500;
    background: rgba(225, 173, 91, 0.1);
    padding: 1px 3px !important;
    border-radius: 2px !important;
    font-size: 7px !important;
}

.history-time {
    color: #A09CB0;
    font-size: 7px !important;
    opacity: 0.6;
    font-style: italic;
    margin-left: auto;
}

.history-url {
    color: #A09CB0;
    font-size: 8px !important;
    opacity: 0.7;
    word-break: break-all;
    line-height: 1.2;
    margin-top: 2px;
}

/* === 播放内容信息样式 === */
.playing-content-info {
    background: rgba(35, 32, 55, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 6px;
    padding: 8px;
    margin-bottom: 8px;
}

.playing-content-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 4px;
}

.playing-label {
    color: #A09CB0;
    font-size: 10px !important;
    font-weight: 500;
    white-space: nowrap;
}

.playing-title {
    color: #F0F0F5;
    font-size: 11px !important; /* 层次4 - 与历史记录标题一致 */
    font-weight: 600;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    margin: 0 8px;
}

.playing-url {
    color: #A09CB0;
    font-size: 8px !important;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-top: 4px;
}

.open-page-btn {
    background: rgba(65, 105, 225, 0.8);
    color: white;
    border: none;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    cursor: pointer;
    font-size: 8px !important;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 2px;
    min-height: auto !important;
    min-width: auto !important;
}

.open-page-btn:hover {
    background: rgba(65, 105, 225, 1);
}

.open-page-icon, .open-page-text {
    font-size: 7px !important;
}

/* === 清空历史按钮 === */
.clear-history-btn {
    background: rgba(231, 76, 60, 0.8);
    color: white;
    border: none;
    padding: 4px 8px !important;
    border-radius: 3px !important;
    cursor: pointer;
    font-size: 8px !important;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 2px;
    margin-top: 8px;
    width: 100%;
    justify-content: center;
}

.clear-history-btn:hover {
    background: rgba(231, 76, 60, 1);
}

.clear-icon, .clear-text {
    font-size: 7px !important;
}

/* === 响应式设计 === */
@media (max-width: 400px) {
    .container {
        padding: 6px;
        gap: 6px;
    }
    
    .card {
        padding: 8px;
    }
    
    .small-btn, .chapter-btn {
        min-width: 24px !important;
        height: 24px !important;
        font-size: 10px !important;
    }
    
    .main-btn {
        min-width: 32px !important;
        height: 32px !important;
        font-size: 12px !important;
    }
}

/* === 消息提示 === */
.message {
    padding: 4px 8px !important;
    font-size: 10px !important;
    margin: 3px 0 !important;
    border-radius: 3px !important;
    text-align: center;
}

.message.success {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.message.error {
    background: rgba(244, 67, 54, 0.2);
    color: #F44336;
    border: 1px solid rgba(244, 67, 54, 0.3);
}

/* === 滚动条样式 === */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(35, 32, 55, 0.3);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}
