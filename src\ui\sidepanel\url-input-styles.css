/* === URL输入和播放区域样式 === */
.url-input-card {
    background: linear-gradient(135deg, rgba(42, 38, 66, 0.65) 0%, rgba(74, 144, 226, 0.05) 100%);
    border: 1px solid rgba(74, 144, 226, 0.2);
}

.url-input-section .section-header {
    margin-bottom: 16px;
}

.url-input-section .section-header h3 {
    margin: 0;
    color: #F0F0F5;
    font-size: 16px;
    font-weight: 600;
}

.url-input-container {
    margin-bottom: 20px;
}

.url-input-label {
    display: block;
    margin-bottom: 8px;
    color: #A09CB0;
    font-size: 14px;
    font-weight: 500;
}

.url-input-row {
    display: flex;
    gap: 8px;
    align-items: center;
}

.url-input {
    flex: 1;
    padding: 10px 12px;
    border: 2px solid rgba(255, 255, 255, 0.12);
    border-radius: 8px;
    font-size: 14px;
    background: rgba(35, 32, 55, 0.6);
    color: #F0F0F5;
    transition: all 0.3s ease;
}

.url-input:focus {
    outline: none;
    border-color: #E1AD5B;
    box-shadow: 0 0 0 3px rgba(225, 173, 91, 0.1);
}

.url-input:invalid {
    border-color: #e74c3c;
}

.play-url-btn {
    padding: 10px 16px;
    background: #E1AD5B;
    color: #10121A;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 80px;
    justify-content: center;
}

.play-url-btn:hover {
    background: #EECB86;
    transform: translateY(-1px);
}

.play-url-btn:disabled {
    background: rgba(160, 156, 176, 0.3);
    cursor: not-allowed;
    transform: none;
}

.play-url-icon {
    font-size: 12px;
}

.url-validation-message {
    margin-top: 6px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
}

.url-validation-message.error {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.url-validation-message.success {
    background: rgba(46, 204, 113, 0.1);
    color: #27ae60;
    border: 1px solid rgba(46, 204, 113, 0.2);
}

.current-page-section {
    padding-top: 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.current-page-info {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.current-page-label {
    color: #A09CB0;
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
}

.current-page-title {
    color: #F0F0F5;
    font-size: 14px;
    font-weight: 600;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.current-page-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.play-current-btn {
    flex: 1;
    padding: 12px 16px;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.play-current-btn:hover {
    background: linear-gradient(135deg, #229954 0%, #27ae60 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.play-current-btn:disabled {
    background: rgba(160, 156, 176, 0.3);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.play-current-icon {
    font-size: 12px;
}

.save-current-btn {
    padding: 12px 16px;
    background: rgba(58, 54, 79, 0.8);
    color: #F0F0F5;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.save-current-btn:hover {
    background: rgba(58, 54, 79, 1);
    transform: translateY(-1px);
}

.save-current-icon {
    font-size: 12px;
}

/* 播放状态信息样式 */
.playback-status-info {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.playing-content-info {
    background: rgba(35, 32, 55, 0.4);
    border-radius: 8px;
    padding: 12px;
    border: 1px solid rgba(225, 173, 91, 0.2);
}

.playing-title-container {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.playing-label {
    color: #A09CB0;
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
}

.playing-title {
    color: #F0F0F5;
    font-size: 14px;
    font-weight: 600;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.playing-url-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.playing-url {
    color: #A09CB0;
    font-size: 12px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    opacity: 0.8;
}

.open-page-btn {
    padding: 6px 12px;
    background: #E1AD5B;
    color: #10121A;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
}

.open-page-btn:hover {
    background: #EECB86;
    transform: translateY(-1px);
}

.open-page-icon {
    font-size: 10px;
}

.open-page-text {
    font-size: 11px;
}
