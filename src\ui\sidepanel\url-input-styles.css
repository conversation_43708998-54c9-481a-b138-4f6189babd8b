/* === URL输入和播放区域样式 === */
.url-input-card {
    background: linear-gradient(135deg, rgba(42, 38, 66, 0.65) 0%, rgba(74, 144, 226, 0.05) 100%);
    border: 1px solid rgba(74, 144, 226, 0.2);
}

.url-input-section .section-header {
    margin-bottom: 16px;
}

.url-input-section .section-header h3 {
    margin: 0;
    color: #F0F0F5;
    font-size: 16px;
    font-weight: 600;
}

.url-input-container {
    margin-bottom: 20px;
}

.smart-url-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid rgba(255, 255, 255, 0.12);
    border-radius: 8px;
    font-size: 14px;
    background: rgba(35, 32, 55, 0.6);
    color: #F0F0F5;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.smart-url-input:focus {
    outline: none;
    border-color: #E1AD5B;
    box-shadow: 0 0 0 3px rgba(225, 173, 91, 0.1);
}

.smart-url-input:invalid {
    border-color: #e74c3c;
}

.smart-url-input::placeholder {
    color: #A09CB0;
    opacity: 0.8;
}

.url-validation-message {
    margin-top: 6px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
}

.url-validation-message.error {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.url-validation-message.success {
    background: rgba(46, 204, 113, 0.1);
    color: #27ae60;
    border: 1px solid rgba(46, 204, 113, 0.2);
}

.current-page-section {
    padding-top: 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.current-page-info {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.current-page-label {
    color: #A09CB0;
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
}

.current-page-title {
    color: #F0F0F5;
    font-size: 14px;
    font-weight: 600;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* === 播放历史记录样式 === */
.playback-history-section {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 16px;
}

.history-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    padding: 8px 0;
    transition: all 0.3s ease;
}

.history-header:hover {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    padding: 8px 12px;
}

.history-title {
    color: #F0F0F5;
    font-size: 14px;
    font-weight: 600;
}

.history-toggle-btn {
    background: none;
    border: none;
    color: #A09CB0;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.history-toggle-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #F0F0F5;
}

.toggle-icon {
    font-size: 12px;
    transition: transform 0.3s ease;
}

.history-toggle-btn.expanded .toggle-icon {
    transform: rotate(180deg);
}

.history-content {
    margin-top: 12px;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
    }
    to {
        opacity: 1;
        max-height: 300px;
    }
}

.history-list {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 12px;
}

.empty-history {
    text-align: center;
    color: #A09CB0;
    font-size: 13px;
    font-style: italic;
    padding: 20px;
    background: rgba(35, 32, 55, 0.3);
    border-radius: 6px;
    border: 1px dashed rgba(255, 255, 255, 0.1);
}

.history-item {
    background: rgba(35, 32, 55, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.history-item:hover {
    background: rgba(35, 32, 55, 0.6);
    border-color: rgba(225, 173, 91, 0.3);
    transform: translateY(-1px);
}

.history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 6px;
}

.history-title-text {
    color: #F0F0F5;
    font-size: 14px;
    font-weight: 600;
    flex: 1;
    margin-right: 8px;
    line-height: 1.3;
}

.history-delete-btn {
    background: none;
    border: none;
    color: #A09CB0;
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.3s ease;
    opacity: 0.7;
}

.history-delete-btn:hover {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    opacity: 1;
}

.history-chapter-info {
    color: #A09CB0;
    font-size: 12px;
    margin-bottom: 4px;
    font-style: italic;
}

.history-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
    font-size: 11px;
}

.history-source {
    font-size: 12px;
    opacity: 0.8;
}

.history-site-name {
    color: #E1AD5B;
    font-weight: 500;
    background: rgba(225, 173, 91, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
}

.history-time {
    color: #A09CB0;
    font-size: 10px;
    opacity: 0.6;
    font-style: italic;
    margin-left: auto;
}

.history-url {
    color: #A09CB0;
    font-size: 10px;
    opacity: 0.7;
    word-break: break-all;
    line-height: 1.2;
    margin-top: 4px;
}

.history-actions {
    display: flex;
    justify-content: center;
    padding-top: 8px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.clear-history-btn {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.clear-history-btn:hover {
    background: rgba(231, 76, 60, 0.2);
    transform: translateY(-1px);
}

.clear-icon {
    font-size: 10px;
}

.clear-text {
    font-size: 11px;
}

/* 播放状态信息样式 */
.playback-status-info {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.playing-content-info {
    background: rgba(35, 32, 55, 0.4);
    border-radius: 8px;
    padding: 12px;
    border: 1px solid rgba(225, 173, 91, 0.2);
}

.playing-title-container {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.playing-label {
    color: #A09CB0;
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
}

.playing-title {
    color: #F0F0F5;
    font-size: 14px;
    font-weight: 600;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.playing-url-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.playing-url {
    color: #A09CB0;
    font-size: 12px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    opacity: 0.8;
}

.open-page-btn {
    padding: 6px 12px;
    background: #E1AD5B;
    color: #10121A;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
}

.open-page-btn:hover {
    background: #EECB86;
    transform: translateY(-1px);
}

.open-page-icon {
    font-size: 10px;
}

.open-page-text {
    font-size: 11px;
}
