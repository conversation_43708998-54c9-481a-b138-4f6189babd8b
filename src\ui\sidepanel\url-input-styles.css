/* === URL输入和播放区域样式（紧凑版） === */
.url-input-card {
    background: linear-gradient(135deg, rgba(42, 38, 66, 0.65) 0%, rgba(74, 144, 226, 0.05) 100%);
    border: 1px solid rgba(74, 144, 226, 0.2);
    padding: 10px !important;
    margin-bottom: 8px !important;
}

.url-input-section .section-header {
    margin-bottom: 8px !important;
}

.url-input-section .section-header h3 {
    margin: 0 !important;
    color: #F0F0F5;
    font-size: 13px !important;
    font-weight: 600;
}

.url-input-container {
    margin-bottom: 10px !important;
}

.smart-url-input {
    width: 100%;
    padding: 8px 10px !important;
    border: 1px solid rgba(255, 255, 255, 0.12) !important;
    border-radius: 6px !important;
    font-size: 11px !important;
    background: rgba(35, 32, 55, 0.6);
    color: #F0F0F5;
    transition: all 0.3s ease;
    box-sizing: border-box;
    min-height: 28px !important;
}

.smart-url-input:focus {
    outline: none;
    border-color: #E1AD5B !important;
    box-shadow: 0 0 0 2px rgba(225, 173, 91, 0.1) !important;
}

.smart-url-input:invalid {
    border-color: #e74c3c !important;
}

.smart-url-input::placeholder {
    color: #A09CB0;
    opacity: 0.8;
    font-size: 9px !important;
}

.url-validation-message {
    margin-top: 3px !important;
    padding: 3px 6px !important;
    border-radius: 3px !important;
    font-size: 9px !important;
    font-weight: 500;
}

.url-validation-message.error {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.url-validation-message.success {
    background: rgba(46, 204, 113, 0.1);
    color: #27ae60;
    border: 1px solid rgba(46, 204, 113, 0.2);
}

.current-page-section {
    padding-top: 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.current-page-info {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.current-page-label {
    color: #A09CB0;
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
}

.current-page-title {
    color: #F0F0F5;
    font-size: 14px;
    font-weight: 600;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* === 播放历史记录样式（紧凑版） === */
.playback-history-section {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 6px !important;
    margin-top: 6px !important;
}

.history-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    padding: 3px 0 !important;
    transition: all 0.3s ease;
}

.history-header:hover {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px !important;
    padding: 3px 6px !important;
}

.history-title {
    color: #F0F0F5;
    font-size: 11px !important;
    font-weight: 600;
}

.history-toggle-btn {
    background: none;
    border: none;
    color: #A09CB0;
    cursor: pointer;
    padding: 2px !important;
    border-radius: 2px !important;
    transition: all 0.3s ease;
    min-height: 16px !important;
    min-width: 16px !important;
}

.history-toggle-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #F0F0F5;
}

.toggle-icon {
    font-size: 8px !important;
    transition: transform 0.3s ease;
}

.history-toggle-btn.expanded .toggle-icon {
    transform: rotate(180deg);
}

.history-content {
    margin-top: 4px !important;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
    }
    to {
        opacity: 1;
        max-height: 300px;
    }
}

.history-list {
    max-height: none !important;
    overflow-y: visible !important;
    margin-bottom: 6px !important;
}

.empty-history {
    text-align: center;
    color: #A09CB0;
    font-size: 9px !important;
    font-style: italic;
    padding: 8px !important;
    background: rgba(35, 32, 55, 0.3);
    border-radius: 3px !important;
    border: 1px dashed rgba(255, 255, 255, 0.1);
}

.history-item {
    background: rgba(35, 32, 55, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 3px !important;
    padding: 6px !important;
    margin-bottom: 4px !important;
    transition: all 0.3s ease;
    cursor: pointer;
}

.history-item:hover {
    background: rgba(35, 32, 55, 0.6);
    border-color: rgba(225, 173, 91, 0.3);
    transform: translateY(-1px);
}

.history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 3px !important;
}

.history-title-text {
    color: #F0F0F5;
    font-size: 10px !important;
    font-weight: 600;
    flex: 1;
    margin-right: 4px !important;
    line-height: 1.2 !important;
}

.history-delete-btn {
    background: none;
    border: none;
    color: #A09CB0;
    cursor: pointer;
    padding: 1px 3px !important;
    border-radius: 2px !important;
    font-size: 8px !important;
    transition: all 0.3s ease;
    opacity: 0.7;
    min-height: 14px !important;
    min-width: 14px !important;
}

.history-delete-btn:hover {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    opacity: 1;
}

.history-chapter-info {
    color: #A09CB0;
    font-size: 9px !important;
    margin-bottom: 2px !important;
    font-style: italic;
}

.history-meta {
    display: flex;
    align-items: center;
    gap: 4px !important;
    margin-bottom: 3px !important;
    font-size: 8px !important;
}

.history-source {
    font-size: 8px !important;
    opacity: 0.8;
}

.history-site-name {
    color: #E1AD5B;
    font-weight: 500;
    background: rgba(225, 173, 91, 0.1);
    padding: 1px 3px !important;
    border-radius: 2px !important;
    font-size: 7px !important;
}

.history-time {
    color: #A09CB0;
    font-size: 7px !important;
    opacity: 0.6;
    font-style: italic;
    margin-left: auto;
}

.history-url {
    color: #A09CB0;
    font-size: 7px !important;
    opacity: 0.7;
    word-break: break-all;
    line-height: 1.1 !important;
    margin-top: 2px !important;
}

.history-actions {
    display: flex;
    justify-content: center;
    padding-top: 4px !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.clear-history-btn {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    padding: 4px 8px !important;
    border-radius: 3px !important;
    cursor: pointer;
    font-size: 8px !important;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 3px !important;
    min-height: 20px !important;
}

.clear-history-btn:hover {
    background: rgba(231, 76, 60, 0.2);
    transform: translateY(-1px);
}

.clear-icon {
    font-size: 7px !important;
}

.clear-text {
    font-size: 7px !important;
}

/* 播放状态信息样式（紧凑版） */
.playback-status-info {
    margin-top: 6px !important;
    padding-top: 6px !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.playing-content-info {
    background: rgba(35, 32, 55, 0.4);
    border-radius: 3px !important;
    padding: 6px !important;
    border: 1px solid rgba(225, 173, 91, 0.2);
}

.playing-title-container {
    margin-bottom: 3px !important;
    display: flex;
    align-items: center;
    gap: 4px !important;
}

.playing-label {
    color: #A09CB0;
    font-size: 9px !important;
    font-weight: 500;
    white-space: nowrap;
}

.playing-title {
    color: #F0F0F5;
    font-size: 10px !important;
    font-weight: 600;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.playing-url-container {
    display: flex;
    align-items: center;
    gap: 4px !important;
}

.playing-url {
    color: #A09CB0;
    font-size: 8px !important;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    opacity: 0.8;
}

.open-page-btn {
    padding: 3px 6px !important;
    background: #E1AD5B;
    color: #10121A;
    border: none;
    border-radius: 3px !important;
    cursor: pointer;
    font-size: 8px !important;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 2px !important;
    white-space: nowrap;
    min-height: 18px !important;
}

.open-page-btn:hover {
    background: #EECB86;
    transform: translateY(-1px);
}

.open-page-icon {
    font-size: 7px !important;
}

.open-page-text {
    font-size: 7px !important;
}
