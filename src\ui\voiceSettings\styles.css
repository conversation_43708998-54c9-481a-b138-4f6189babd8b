/* 语音设置页面样式 */

/* 基础样式重置 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 11px;
    line-height: 1.3;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #F0F0F5;
    min-height: 100vh;
    padding: 16px;
}

.voice-settings-container {
    max-width: 800px;
    margin: 0 auto;
    background: rgba(35, 32, 55, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

/* 头部样式 */
.voice-settings-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.voice-settings-header h2 {
    font-size: 16px;
    font-weight: 600;
    color: #F0F0F5;
}

.close-btn {
    background: rgba(231, 76, 60, 0.8);
    color: white;
    border: none;
    border-radius: 6px;
    width: 32px;
    height: 32px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(231, 76, 60, 1);
}

/* 卡片样式 */
.card {
    background: rgba(35, 32, 55, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
}

/* 搜索框样式 */
.search-box {
    margin-bottom: 12px;
}

.voice-search-input {
    width: 100%;
    background: rgba(35, 32, 55, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 12px;
    color: #F0F0F5;
}

.voice-search-input::placeholder {
    color: #A09CB0;
    opacity: 0.8;
}

/* 筛选标签样式 */
.filter-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
    flex-wrap: wrap;
}

.filter-tab {
    background: rgba(65, 105, 225, 0.3);
    color: #F0F0F5;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-tab.active {
    background: rgba(65, 105, 225, 0.8);
}

.filter-tab:hover {
    background: rgba(65, 105, 225, 0.6);
}

/* 语音列表样式 */
.voices-list {
    max-height: 400px;
    overflow-y: auto;
}

.voice-item {
    background: rgba(35, 32, 55, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
}

.voice-item:hover {
    background: rgba(35, 32, 55, 0.6);
    border-color: rgba(255, 255, 255, 0.15);
}

.voice-item.selected {
    background: rgba(65, 105, 225, 0.2);
    border-color: rgba(65, 105, 225, 0.5);
}

.voice-info {
    flex: 1;
}

.voice-name {
    font-size: 12px;
    font-weight: 600;
    color: #F0F0F5;
    margin-bottom: 2px;
}

.voice-details {
    font-size: 10px;
    color: #A09CB0;
}

.voice-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.preview-btn {
    background: rgba(225, 173, 91, 0.8);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.preview-btn:hover {
    background: rgba(225, 173, 91, 1);
}

.select-btn {
    background: rgba(65, 105, 225, 0.8);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.select-btn:hover {
    background: rgba(65, 105, 225, 1);
}

/* AI语音区域样式 */
.ai-tts-card {
    opacity: 0.6;
}

.ai-tts-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.ai-tts-header h3 {
    font-size: 14px;
    font-weight: 600;
    color: #F0F0F5;
}

.badge {
    background: rgba(225, 173, 91, 0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 8px;
    font-weight: 600;
}

.ai-tts-message {
    font-size: 11px;
    color: #A09CB0;
    font-style: italic;
}

/* 保存按钮样式 */
.save-button-container {
    text-align: center;
    margin-top: 20px;
}

.save-settings-btn {
    background: rgba(76, 175, 80, 0.8);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 24px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.save-settings-btn:hover {
    background: rgba(76, 175, 80, 1);
    transform: translateY(-1px);
}

/* 加载状态样式 */
.loading-message {
    text-align: center;
    color: #A09CB0;
    font-size: 11px;
    font-style: italic;
    padding: 20px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(35, 32, 55, 0.3);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 600px) {
    body {
        padding: 8px;
    }
    
    .voice-settings-container {
        padding: 12px;
    }
    
    .voice-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .voice-controls {
        width: 100%;
        justify-content: flex-end;
    }
}
