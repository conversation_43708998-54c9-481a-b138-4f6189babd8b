<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基础播放功能测试 - 神灯AI·灵阅</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2d3436;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #74b9ff;
        }
        .status-section {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border: 1px solid #bbdefb;
        }
        .status-section h4 {
            color: #1976d2;
            margin-top: 0;
        }
        .article-content {
            background: #f0f8ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #b3d9ff;
        }
        .debug-info {
            background: #2d3436;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: white;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .debug-info h4 {
            color: #74b9ff;
            margin-top: 0;
            font-family: 'Segoe UI', sans-serif;
        }
        .test-button {
            background: #74b9ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s ease;
        }
        .test-button:hover {
            background: #0984e3;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 基础播放功能测试</h1>
        
        <div class="test-section">
            <h3>🎯 测试目标</h3>
            <p>验证修复重复播放Bug后，基础播放功能是否正常工作。</p>
            <ul>
                <li>点击播放按钮是否有反应</li>
                <li>播放状态是否正确显示</li>
                <li>播放控制是否正常</li>
                <li>连续播放是否只播放一次</li>
            </ul>
        </div>

        <div class="status-section">
            <h4>📊 当前状态检查</h4>
            <p>请打开浏览器开发者工具的控制台，查看详细的调试信息。</p>
            <button class="test-button" onclick="checkPluginStatus()">检查插件状态</button>
            <button class="test-button" onclick="testBasicPlayback()">测试基础播放</button>
            <button class="test-button" onclick="clearConsole()">清空控制台</button>
        </div>

        <div class="debug-info" id="debug-output">
            <h4>🐛 调试信息</h4>
            <div id="debug-content">等待测试...</div>
        </div>

        <div class="article-content">
            <h2>📖 测试文章：基础播放功能验证</h2>
            <p>这是一篇用于测试基础播放功能的文章。如果插件工作正常，您应该能够点击播放按钮开始朗读这段内容。</p>
            
            <h3>第一段：功能测试</h3>
            <p>神灯AI·灵阅是一个强大的网页朗读插件，支持智能语音播放、连续章节阅读、语速调整等功能。通过先进的文本解析技术，它能够准确识别网页中的主要内容，为用户提供流畅的听书体验。</p>
            
            <h3>第二段：技术特点</h3>
            <p>插件采用了Chrome扩展技术，结合了Readability内容解析、Chrome TTS语音合成、以及智能的章节预加载机制。这些技术的结合确保了播放的连续性和稳定性，让用户能够享受不间断的阅读体验。</p>
            
            <h3>第三段：用户体验</h3>
            <p>经过不断的优化和改进，插件现在具有更好的用户界面、更准确的内容识别、更流畅的播放控制。特别是在修复了重复播放的Bug之后，连续播放功能变得更加可靠和高效。</p>
            
            <h3>第四段：未来展望</h3>
            <p>未来，我们将继续改进插件的功能，包括更多的语音选项、更智能的内容识别、更个性化的设置选项。我们的目标是为用户提供最佳的网页朗读体验，让知识的获取变得更加便捷和愉悦。</p>
        </div>

        <div class="test-section">
            <h3>🧪 测试步骤</h3>
            <ol>
                <li><strong>打开侧边栏：</strong>点击浏览器工具栏中的插件图标</li>
                <li><strong>检查状态：</strong>观察侧边栏是否正常显示</li>
                <li><strong>点击播放：</strong>点击播放按钮，观察是否开始朗读</li>
                <li><strong>控制测试：</strong>测试暂停、继续、停止功能</li>
                <li><strong>语速调整：</strong>测试实时语速调整功能</li>
                <li><strong>连续播放：</strong>如果有多章节，测试连续播放</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🔍 问题排查</h3>
            <p>如果播放按钮没有反应，请检查：</p>
            <ul>
                <li><strong>插件加载：</strong>确认插件已正确加载</li>
                <li><strong>权限设置：</strong>确认插件有必要的权限</li>
                <li><strong>页面内容：</strong>确认页面内容已正确解析</li>
                <li><strong>TTS支持：</strong>确认浏览器支持语音合成</li>
                <li><strong>控制台错误：</strong>查看是否有JavaScript错误</li>
            </ul>

            <h4>🆕 最新修复内容：</h4>
            <ul>
                <li>✅ 修复了异步响应通道关闭问题</li>
                <li>✅ 添加了内容解析超时机制（10秒）</li>
                <li>✅ 改进了错误处理和状态管理</li>
                <li>✅ 修复了Service Worker注册问题</li>
                <li>✅ 添加了详细的调试日志</li>
            </ul>
        </div>
    </div>

    <script>
        console.log('🔧 基础播放功能测试页面已加载');
        
        function updateDebugOutput(message) {
            const debugContent = document.getElementById('debug-content');
            const timestamp = new Date().toLocaleTimeString();
            debugContent.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            debugContent.scrollTop = debugContent.scrollHeight;
        }
        
        function checkPluginStatus() {
            console.log('🔍 检查插件状态...');
            updateDebugOutput('🔍 开始检查插件状态');
            
            // 检查Chrome扩展API是否可用
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                updateDebugOutput('✅ Chrome扩展API可用');
                console.log('✅ Chrome扩展API可用');
                
                // 尝试发送消息到背景脚本
                try {
                    chrome.runtime.sendMessage({ action: 'getState' }, (response) => {
                        if (chrome.runtime.lastError) {
                            updateDebugOutput('❌ 无法连接到背景脚本: ' + chrome.runtime.lastError.message);
                            console.error('❌ 背景脚本连接失败:', chrome.runtime.lastError);
                        } else {
                            updateDebugOutput('✅ 背景脚本连接正常');
                            updateDebugOutput('📊 当前状态: ' + JSON.stringify(response, null, 2));
                            console.log('✅ 背景脚本响应:', response);
                        }
                    });
                } catch (error) {
                    updateDebugOutput('❌ 发送消息时出错: ' + error.message);
                    console.error('❌ 发送消息出错:', error);
                }
            } else {
                updateDebugOutput('❌ Chrome扩展API不可用');
                console.error('❌ Chrome扩展API不可用');
            }
            
            // 检查TTS API
            if (typeof speechSynthesis !== 'undefined') {
                updateDebugOutput('✅ 浏览器TTS API可用');
                console.log('✅ TTS API可用');
            } else {
                updateDebugOutput('❌ 浏览器不支持TTS API');
                console.error('❌ TTS API不可用');
            }
        }
        
        function testBasicPlayback() {
            console.log('🎵 测试基础播放功能...');
            updateDebugOutput('🎵 开始测试基础播放功能');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                try {
                    // 首先尝试解析当前页面内容
                    chrome.runtime.sendMessage({ action: 'parseContent' }, (response) => {
                        if (chrome.runtime.lastError) {
                            updateDebugOutput('❌ 解析内容失败: ' + chrome.runtime.lastError.message);
                            console.error('❌ 解析内容失败:', chrome.runtime.lastError);
                        } else {
                            updateDebugOutput('✅ 内容解析成功');
                            console.log('✅ 内容解析响应:', response);
                            
                            // 等待一下再尝试播放
                            setTimeout(() => {
                                chrome.runtime.sendMessage({ action: 'startReading' }, (playResponse) => {
                                    if (chrome.runtime.lastError) {
                                        updateDebugOutput('❌ 开始播放失败: ' + chrome.runtime.lastError.message);
                                        console.error('❌ 开始播放失败:', chrome.runtime.lastError);
                                    } else {
                                        updateDebugOutput('✅ 播放请求已发送');
                                        updateDebugOutput('📊 播放响应: ' + JSON.stringify(playResponse));
                                        console.log('✅ 播放响应:', playResponse);
                                    }
                                });
                            }, 1000);
                        }
                    });
                } catch (error) {
                    updateDebugOutput('❌ 测试播放时出错: ' + error.message);
                    console.error('❌ 测试播放出错:', error);
                }
            } else {
                updateDebugOutput('❌ 无法测试播放：Chrome扩展API不可用');
                console.error('❌ 无法测试播放：扩展API不可用');
            }
        }
        
        function clearConsole() {
            console.clear();
            document.getElementById('debug-content').innerHTML = '控制台已清空，等待新的测试...';
            console.log('🔧 控制台已清空，准备新的测试');
        }
        
        // 页面加载完成时自动检查状态
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 基础播放功能测试页面准备就绪');
            updateDebugOutput('📄 测试页面已加载，准备开始测试');
            
            // 3秒后自动检查插件状态
            setTimeout(() => {
                updateDebugOutput('🔄 自动检查插件状态...');
                checkPluginStatus();
            }, 3000);
        });
        
        // 监听来自背景脚本的消息
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                console.log('📨 收到背景脚本消息:', message);
                updateDebugOutput('📨 收到消息: ' + JSON.stringify(message));
                return true;
            });
        }
    </script>
</body>
</html>
