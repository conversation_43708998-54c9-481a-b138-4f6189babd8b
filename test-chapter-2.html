<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第二章：URL播放历史记录验证 - 神灯AI·灵阅</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2d3436;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #a29bfe;
        }
        .url-example {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border: 1px solid #bbdefb;
            font-family: 'Courier New', monospace;
            word-break: break-all;
        }
        .article-content {
            background: #f3e5f5;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #ce93d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 第二章：URL播放历史记录验证</h1>
        
        <div class="test-section">
            <h3>🎯 URL播放测试</h3>
            <p>这个页面用于测试URL输入播放的历史记录功能。请在侧边栏的URL输入框中输入此页面的地址进行测试。</p>
        </div>

        <div class="url-example">
            <strong>测试URL（复制此地址到侧边栏）：</strong><br>
            <span id="current-url">正在获取当前页面URL...</span>
        </div>

        <div class="test-section">
            <h3>📋 测试步骤</h3>
            <ol>
                <li><strong>复制URL：</strong>复制上方显示的URL地址</li>
                <li><strong>粘贴到侧边栏：</strong>在URL输入框中粘贴地址</li>
                <li><strong>点击播放：</strong>点击播放按钮开始播放</li>
                <li><strong>检查历史记录：</strong>查看是否出现🔗标识的记录</li>
                <li><strong>验证信息：</strong>确认章节、站点等信息正确</li>
            </ol>
        </div>

        <div class="article-content">
            <h2>📖 第二章：URL播放功能深度测试</h2>
            <p>这是第二章的内容，专门用于测试URL输入播放功能。当您通过URL输入框播放此页面时，历史记录应该显示🔗图标，表明这是通过URL输入播放的。</p>
            
            <h3>第一节：URL播放的特点</h3>
            <p>通过URL输入播放具有以下特点：</p>
            <ul>
                <li><strong>独立性：</strong>不依赖当前浏览的页面</li>
                <li><strong>灵活性：</strong>可以播放任意有效的网页内容</li>
                <li><strong>标识性：</strong>在历史记录中有专门的🔗标识</li>
                <li><strong>缓存性：</strong>解析结果会被缓存以提高效率</li>
            </ul>
            
            <h3>第二节：历史记录区别</h3>
            <p>URL播放和当前页面播放在历史记录中的区别：</p>
            <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
                <tr style="background: #f5f5f5;">
                    <th style="border: 1px solid #ddd; padding: 8px;">播放方式</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">图标</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">来源标识</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">特点</th>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">当前页面播放</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">📄</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">current_page</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">播放正在浏览的页面</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">URL输入播放</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">🔗</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">url_input</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">播放指定URL的内容</td>
                </tr>
            </table>
            
            <h3>第三节：测试验证要点</h3>
            <p>在测试URL播放功能时，请注意验证以下要点：</p>
            <ol>
                <li><strong>章节识别：</strong>应该正确识别"第二章"</li>
                <li><strong>来源标识：</strong>应该显示🔗图标</li>
                <li><strong>站点信息：</strong>应该显示当前域名</li>
                <li><strong>时间记录：</strong>应该记录播放时间</li>
                <li><strong>去重功能：</strong>重复播放同一URL应该更新而不重复</li>
            </ol>
            
            <p>通过这些测试，我们可以确保URL播放功能和历史记录系统的完整性和准确性。</p>
        </div>

        <div class="test-section">
            <h3>🔍 预期结果</h3>
            <ul>
                <li>历史记录标题：第二章：URL播放历史记录验证</li>
                <li>章节信息：第二章</li>
                <li>来源图标：🔗</li>
                <li>站点信息：根据当前域名显示</li>
                <li>播放时间：当前时间</li>
            </ul>
        </div>
    </div>

    <script>
        // 显示当前页面URL
        document.addEventListener('DOMContentLoaded', function() {
            const urlElement = document.getElementById('current-url');
            if (urlElement) {
                urlElement.textContent = window.location.href;
            }
            
            console.log('🔗 第二章测试页面已加载');
            console.log('📋 当前URL:', window.location.href);
            console.log('🔍 请复制URL到侧边栏进行测试');
        });
        
        // 添加复制功能
        document.getElementById('current-url').addEventListener('click', function() {
            navigator.clipboard.writeText(window.location.href).then(function() {
                console.log('✅ URL已复制到剪贴板');
                alert('URL已复制到剪贴板！');
            }).catch(function(err) {
                console.error('❌ 复制失败:', err);
            });
        });
    </script>
</body>
</html>
