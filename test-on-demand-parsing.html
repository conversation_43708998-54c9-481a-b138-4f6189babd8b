<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按需解析测试页面 - 神灯AI·灵阅</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2d3436;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2em;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #74b9ff;
        }
        .test-title {
            color: #2d3436;
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .test-icon {
            font-size: 1.5em;
        }
        .expected-behavior {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .expected-behavior h4 {
            color: #155724;
            margin-top: 0;
        }
        .expected-behavior ul {
            margin-bottom: 0;
            color: #155724;
        }
        .console-check {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .console-check h4 {
            color: #856404;
            margin-top: 0;
        }
        .console-check code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        .warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning h4 {
            color: #721c24;
            margin-top: 0;
        }
        .article-content {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #bbdefb;
        }
        .step-list {
            list-style: none;
            padding: 0;
        }
        .step-list li {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 3px solid #74b9ff;
            position: relative;
            padding-left: 50px;
        }
        .step-number {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: #74b9ff;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 按需解析测试页面</h1>
        
        <div class="test-section">
            <div class="test-title">
                <span class="test-icon">🎯</span>
                测试目标
            </div>
            <p>验证神灯AI·灵阅扩展的新预读逻辑：页面加载后不自动解析内容，只有在用户点击播放按钮时才开始解析和播放。</p>
        </div>

        <div class="expected-behavior">
            <h4>✅ 预期行为</h4>
            <ul>
                <li><strong>页面加载时：</strong>扩展不会自动解析页面内容</li>
                <li><strong>打开侧边栏：</strong>不会触发内容解析，状态为idle</li>
                <li><strong>点击播放按钮：</strong>才开始解析页面内容并播放</li>
                <li><strong>URL输入播放：</strong>解析指定URL的内容并播放</li>
            </ul>
        </div>

        <div class="console-check">
            <h4>🔍 控制台检查</h4>
            <p>打开浏览器开发者工具（F12），在控制台中查看以下日志：</p>
            <ul>
                <li><strong>页面加载时：</strong>应该看到 <code>神灯AI·灵阅：内容解析脚本 parser.js 已加载</code></li>
                <li><strong>不应该看到：</strong><code>神灯AI·灵阅：开始解析页面内容</code></li>
                <li><strong>点击播放后：</strong>才应该看到解析相关的日志</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">
                <span class="test-icon">📋</span>
                测试步骤
            </div>
            
            <ol class="step-list">
                <li>
                    <span class="step-number">1</span>
                    <strong>加载页面</strong> - 刷新此页面，观察控制台日志
                </li>
                <li>
                    <span class="step-number">2</span>
                    <strong>打开侧边栏</strong> - 点击扩展图标打开侧边栏
                </li>
                <li>
                    <span class="step-number">3</span>
                    <strong>检查状态</strong> - 确认侧边栏显示为idle状态，没有解析内容
                </li>
                <li>
                    <span class="step-number">4</span>
                    <strong>点击播放</strong> - 点击播放按钮，观察是否开始解析
                </li>
                <li>
                    <span class="step-number">5</span>
                    <strong>测试URL播放</strong> - 在URL输入框中输入网址并播放
                </li>
            </ol>
        </div>

        <div class="warning">
            <h4>⚠️ 注意事项</h4>
            <ul>
                <li>确保使用的是修改后的扩展版本</li>
                <li>如果看到自动解析的日志，说明修改未生效</li>
                <li>测试时请关注控制台的详细日志输出</li>
                <li>可以多次刷新页面验证一致性</li>
            </ul>
        </div>

        <div class="article-content">
            <h2>📖 测试文章内容</h2>
            <p>这是一个用于测试按需解析功能的示例文章。在新的逻辑下，这段内容不会在页面加载时自动被解析，只有在用户主动点击播放按钮时才会被处理。</p>
            
            <h3>按需解析的优势</h3>
            <p>按需解析带来了以下优势：</p>
            <ul>
                <li><strong>性能优化：</strong>减少不必要的计算资源消耗</li>
                <li><strong>用户控制：</strong>用户明确表达播放意图时才开始处理</li>
                <li><strong>网络友好：</strong>避免在用户不需要时消耗网络资源</li>
                <li><strong>电池节省：</strong>减少后台处理，延长设备电池寿命</li>
            </ul>
            
            <h3>技术实现</h3>
            <p>新的实现方式通过以下技术手段实现按需解析：</p>
            <ol>
                <li><strong>消息驱动：</strong>内容脚本只在收到特定消息时才开始解析</li>
                <li><strong>状态管理：</strong>后台脚本维护loading状态，确保解析完成后自动播放</li>
                <li><strong>智能判断：</strong>播放按钮智能判断是否需要触发解析</li>
                <li><strong>缓存机制：</strong>解析结果缓存，避免重复解析</li>
            </ol>
            
            <p>这种设计模式更符合现代Web应用的最佳实践，提供了更好的用户体验和系统性能。</p>
        </div>

        <div class="test-section">
            <div class="test-title">
                <span class="test-icon">🧪</span>
                验证结果
            </div>
            <p>如果测试通过，您应该观察到：</p>
            <ul>
                <li>页面加载完成后，控制台没有解析相关的日志</li>
                <li>侧边栏打开时状态为idle，没有文章信息</li>
                <li>只有点击播放按钮后才开始解析和播放</li>
                <li>URL输入播放功能正常工作</li>
            </ul>
        </div>
    </div>

    <script>
        // 页面加载完成时的日志
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 测试页面已加载完成');
            console.log('🔍 请检查是否有自动解析的日志出现');
            
            // 5秒后提醒检查
            setTimeout(() => {
                console.log('⏰ 页面已加载5秒，如果没有看到解析日志，说明按需解析工作正常');
            }, 5000);
        });
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                console.log('👁️ 页面变为可见状态');
            } else {
                console.log('🙈 页面变为隐藏状态');
            }
        });
    </script>
</body>
</html>
