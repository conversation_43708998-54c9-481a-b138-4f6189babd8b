<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面 - 神灯AI·灵阅</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .article {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #4a90e2;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
        }
        p {
            margin: 15px 0;
            text-align: justify;
        }
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
        }
        .test-links {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-links a {
            display: block;
            margin: 5px 0;
            color: #4a90e2;
            text-decoration: none;
        }
        .test-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <article class="article">
        <h1>神灯AI·灵阅 URL播放功能测试页面</h1>
        
        <div class="highlight">
            <strong>测试说明：</strong>这是一个专门用于测试神灯AI·灵阅扩展URL播放功能的页面。您可以使用这个页面来验证新增的URL输入和播放功能是否正常工作。
        </div>
        
        <h2>功能测试指南</h2>

        <p>欢迎使用神灯AI·灵阅的智能播放功能！新的交互逻辑更加简洁直观，一个播放按钮智能判断播放内容，并提供播放历史记录管理。</p>

        <h2>1. 智能播放测试</h2>
        <p><strong>URL播放：</strong>在侧边栏的URL输入框中输入网址，然后点击播放按钮，系统会播放指定网址的内容。</p>
        <p><strong>当前页面播放：</strong>保持URL输入框为空，点击播放按钮，系统会播放当前页面的内容。</p>
        <p><strong>快捷操作：</strong>在URL输入框中按回车键可以直接触发播放。</p>

        <h2>2. 播放历史记录</h2>
        <p>侧边栏下方有一个可折叠的"播放历史"区域，显示最近播放的内容：</p>
        <ul>
            <li><strong>历史记录：</strong>显示解析后的标题、章节信息（如果有）、网址和播放时间</li>
            <li><strong>快速播放：</strong>点击历史记录项目可以快速重新播放</li>
            <li><strong>删除记录：</strong>每个历史项目右上角有删除按钮</li>
            <li><strong>清空历史：</strong>可以一键清空所有历史记录</li>
        </ul>
        
        <h2>3. 播放状态管理</h2>
        <p>在播放过程中，您可以看到播放状态信息，包括正在播放的内容标题和网址。点击"打开页面"按钮可以在新标签页中打开正在播放的页面，这样您就可以同时浏览和收听内容。</p>
        
        <h2>4. 异步播放特性</h2>
        <p>新的播放功能完全独立于当前页面状态。这意味着您可以在播放内容的同时自由浏览其他网页，播放不会被中断。这种设计类似于音乐播放器的体验，让您享受真正的后台播放功能。</p>
        
        <div class="test-links">
            <strong>推荐测试网址：</strong>
            <a href="https://www.example.com" target="_blank">https://www.example.com</a>
            <a href="https://zh.wikipedia.org/wiki/人工智能" target="_blank">https://zh.wikipedia.org/wiki/人工智能</a>
            <a href="https://news.ycombinator.com" target="_blank">https://news.ycombinator.com</a>
        </div>
        
        <h2>技术特性</h2>
        <p>这个新功能基于以下技术特性构建：</p>
        <ul>
            <li><strong>URL验证：</strong>自动验证输入的网址格式，确保只播放有效的HTTP/HTTPS链接</li>
            <li><strong>内容缓存：</strong>智能缓存已解析的网页内容，提高播放效率</li>
            <li><strong>异步处理：</strong>在后台处理网页加载和内容解析，不影响用户当前的浏览体验</li>
            <li><strong>状态同步：</strong>实时同步播放状态，确保UI显示与实际播放状态一致</li>
            <li><strong>错误处理：</strong>完善的错误处理机制，对无法解析的网页给出友好的提示</li>
        </ul>
        
        <h2>使用建议</h2>
        <p>为了获得最佳的使用体验，建议您：</p>
        <ol>
            <li>确保输入的网址是可访问的公开网页</li>
            <li>优先选择包含丰富文本内容的页面进行播放</li>
            <li>在播放过程中可以随时使用播放控制按钮进行暂停、继续或停止操作</li>
            <li>利用"打开页面"功能在播放的同时查看原始内容</li>
        </ol>
        
        <div class="highlight">
            <strong>注意：</strong>某些网站可能由于安全策略限制而无法正常解析内容。如果遇到播放失败的情况，请尝试其他网址或使用"播放当前页"功能。
        </div>
        
        <h2>反馈与改进</h2>
        <p>这是神灯AI·灵阅的一个重要功能更新，我们非常重视您的使用体验。如果您在使用过程中遇到任何问题或有改进建议，欢迎通过相关渠道反馈给我们。</p>
        
        <p>感谢您使用神灯AI·灵阅，希望这个新功能能够为您带来更好的阅读和收听体验！</p>
    </article>
</body>
</html>
