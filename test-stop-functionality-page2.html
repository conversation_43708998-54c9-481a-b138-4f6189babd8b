<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试停止功能 - 第二页</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.8;
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2d3436;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }
        .test-info {
            background: #e8f5e8;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #4caf50;
        }
        .content {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin: 20px 0;
            border: 1px solid #dee2e6;
            text-align: justify;
        }
        .navigation {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .nav-link {
            background: #11998e;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: background 0.3s ease;
        }
        .nav-link:hover {
            background: #0d7377;
            color: white;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>✅ 停止功能测试 - 第二页</h1>
        
        <div class="test-info">
            <h3>🎯 验证点</h3>
            <p>如果你在这个页面点击播放，应该听到"这是第二页的内容"。</p>
            <p>如果听到的是"这是第一页的内容"，说明停止功能有问题。</p>
        </div>

        <div class="navigation">
            <a href="test-stop-functionality.html" class="nav-link">📄 第一页</a>
            <a href="test-stop-functionality-page3.html" class="nav-link">📄 第三页</a>
            <a href="javascript:void(0)" class="nav-link" onclick="testPlayback()">🎵 测试播放</a>
        </div>

        <div class="content">
            <h2>第二页测试内容</h2>
            
            <p><strong>这是第二页的内容。</strong>如果停止功能正常工作，你在这个页面点击播放应该听到这段文字。</p>
            
            <p>测试验证要点：</p>
            <ul>
                <li><strong>正确行为：</strong>播放"这是第二页的内容"</li>
                <li><strong>错误行为：</strong>播放"这是第一页的内容"</li>
            </ul>
            
            <p>如果播放的是第一页内容，说明：</p>
            <ol>
                <li>停止按钮没有清除 tabContentCache</li>
                <li>播放时使用了缓存的第一页内容</li>
                <li>currentReadingTabId 没有被重置</li>
            </ol>
            
            <p>正确的停止逻辑应该：</p>
            <ul>
                <li>设置 readingState = 'idle'</li>
                <li>清空 tabContentCache = {}</li>
                <li>重置 currentReadingTabId = null</li>
                <li>重置 currentPosition = 0</li>
                <li>清除 currentUtterance = null</li>
            </ul>
            
            <p>这样下次播放时会重新解析当前页面内容，而不是使用旧缓存。</p>
            
            <p>第二页的内容与第一页明显不同，通过听觉很容易区分。如果停止功能正常，你应该能清楚地听到"这是第二页的内容"这句话。</p>
        </div>

        <div class="success">
            <h3>✅ 成功标准</h3>
            <p>如果你在这个页面播放时听到"这是第二页的内容"，说明停止功能工作正常！</p>
            <p>这意味着停止按钮正确地清除了所有状态和缓存。</p>
        </div>
    </div>

    <script>
        console.log('📄 第二页测试页面已加载');
        console.log('🎯 当前页面标题:', document.title);
        
        function testPlayback() {
            alert('请点击插件的播放按钮进行测试！\n\n如果听到"这是第二页的内容"，说明停止功能正常。\n如果听到"这是第一页的内容"，说明停止功能有问题。');
        }
        
        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ 第二页测试页面准备就绪');
            console.log('🔍 验证点: 播放应该是第二页内容，不是第一页');
        });
    </script>
</body>
</html>
