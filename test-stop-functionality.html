<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试停止功能 - 第一页</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.8;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2d3436;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }
        .test-info {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
        .test-steps {
            background: #f3e5f5;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #9c27b0;
        }
        .expected-result {
            background: #e8f5e8;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #4caf50;
        }
        .content {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin: 20px 0;
            border: 1px solid #dee2e6;
            text-align: justify;
        }
        .navigation {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .nav-link {
            background: #4a90e2;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: background 0.3s ease;
        }
        .nav-link:hover {
            background: #357abd;
            color: white;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 600;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛑 停止功能测试 - 第一页</h1>
        
        <div class="test-info">
            <h3>🎯 测试目标</h3>
            <p>验证停止按钮能够完全清除状态，切换页面后播放新内容而不是旧内容。</p>
            <ul>
                <li><strong>当前页面：</strong>第一页测试内容</li>
                <li><strong>测试场景：</strong>播放 → 停止 → 切换页面 → 播放</li>
                <li><strong>预期行为：</strong>停止后切换页面，播放的应该是新页面内容</li>
            </ul>
        </div>

        <div class="test-steps">
            <h3>🧪 测试步骤</h3>
            <ol>
                <li><strong>播放当前页面：</strong>点击插件播放按钮，开始播放第一页内容</li>
                <li><strong>停止播放：</strong>点击停止按钮（不是暂停）</li>
                <li><strong>切换到第二页：</strong>点击下面的"第二页"链接</li>
                <li><strong>播放第二页：</strong>点击播放按钮</li>
                <li><strong>验证内容：</strong>应该播放第二页的内容，而不是第一页的</li>
            </ol>
        </div>

        <div class="navigation">
            <a href="test-stop-functionality-page2.html" class="nav-link">📄 第二页</a>
            <a href="test-stop-functionality-page3.html" class="nav-link">📄 第三页</a>
            <a href="javascript:void(0)" class="nav-link" onclick="changeTitle()">🔄 改变标题</a>
        </div>

        <div class="warning">
            <strong>⚠️ 重要提示：</strong>
            <ul>
                <li>停止按钮应该完全清除播放状态和缓存</li>
                <li>暂停按钮保留播放位置，停止按钮不保留</li>
                <li>停止后切换页面，播放的应该是新页面内容</li>
                <li>如果播放的还是旧内容，说明缓存没有被清除</li>
            </ul>
        </div>

        <div class="content">
            <h2>第一页测试内容</h2>
            
            <p><strong>这是第一页的内容。</strong>如果你在第二页或第三页点击播放，但听到的是这段文字，说明停止功能有问题，缓存没有被正确清除。</p>
            
            <p>正确的行为应该是：</p>
            <ul>
                <li>在第一页播放 → 听到"这是第一页的内容"</li>
                <li>点击停止按钮 → 播放停止，状态清除</li>
                <li>切换到第二页 → 页面内容改变</li>
                <li>点击播放按钮 → 听到"这是第二页的内容"</li>
            </ul>
            
            <p>如果在第二页播放时听到的还是"这是第一页的内容"，说明：</p>
            <ol>
                <li>停止按钮没有清除内容缓存</li>
                <li>播放时使用了旧的缓存内容</li>
                <li>需要修复停止逻辑</li>
            </ol>
            
            <p>这个测试页面的内容很长，足够播放一段时间。你可以在播放过程中点击停止按钮，然后切换到其他页面测试。</p>
            
            <p>停止功能的核心要求：</p>
            <ul>
                <li><strong>完全清除状态：</strong>readingState = 'idle'</li>
                <li><strong>清除内容缓存：</strong>tabContentCache = {}</li>
                <li><strong>重置标签页ID：</strong>currentReadingTabId = null</li>
                <li><strong>重置播放位置：</strong>currentPosition = 0</li>
            </ul>
            
            <p>这样确保下次播放时会重新解析当前页面的内容，而不是使用旧的缓存。</p>
        </div>

        <div class="expected-result">
            <h3>✅ 预期结果</h3>
            <ul>
                <li><strong>第一页播放：</strong>听到"这是第一页的内容"</li>
                <li><strong>停止后状态：</strong>显示"已停止"，所有缓存清除</li>
                <li><strong>第二页播放：</strong>听到"这是第二页的内容"</li>
                <li><strong>第三页播放：</strong>听到"这是第三页的内容"</li>
                <li><strong>控制台日志：</strong>显示"所有状态和缓存已清除"</li>
            </ul>
        </div>
    </div>

    <script>
        console.log('📄 第一页测试页面已加载');
        console.log('🎯 当前页面标题:', document.title);
        
        function changeTitle() {
            const newTitle = "测试停止功能 - 第一页（标题已修改）";
            document.title = newTitle;
            document.querySelector('h1').textContent = "🛑 停止功能测试 - 第一页（标题已修改）";
            console.log('🔄 页面标题已修改为:', newTitle);
            alert('页面标题已修改！\n\n现在可以测试停止后是否会重新解析页面内容。');
        }
        
        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ 第一页测试页面准备就绪');
            console.log('📋 测试步骤: 播放 → 停止 → 切换页面 → 播放');
        });
    </script>
</body>
</html>
